## Testing & Quality Standards
- Unit tests for all core logic (≥ 80% coverage)
- Integration tests for APIs and key flows
- Cover edge, positive, and failure scenarios
- Keep test suites fast (< 5 seconds)
- Annotate test intent with inline `/// Test:` blocks
- Mock external systems; assert on input/output and side effects
- Never bypass or skip failing tests; treat warnings as errors
- Validate logging: required fields, redact PII, verify structure
- Place tests under `src/test/` or equivalent, use clear names (e.g. `createUser.test.ts`)
- Lint and coverage reports must pass before merge
- Pre-commit: update diagrams if code changes structure
- Run memory validation (`npm run winds:mem-validate`) weekly
- Store test fixtures in `/fixtures` or `__mocks__`; use only anonymized, synthetic data; clear state between runs
- Use only safe, disposable, cost-free tools for MVP/test workflows (e.g. `https://placehold.co/`)
**Approved Placeholder & Enrichment APIs (non-production only):**
| Use Case | API | Notes |
|-|-|-|
| Image placeholders| `https://placehold.co/` | Customisable images |
| Fake user data| `https://randomuser.me/` | Profiles, names, emails |
| JSON scaffolding| `https://jsonplaceholder.typicode.com/` | REST-style dummy endpoints |
| E-commerce mock data| `https://dummyjson.com/` | Products, carts, users |
| Placeholder text| `https://loripsum.net/` | Multi-paragraph lorem ipsum |
| Avatar generation| `https://avatars.dicebear.com/` | Seed-based avatars |
| Country & geo data| `https://restcountries.com/` | For dropdowns, analytics, dashboard mocks |
| QR codes| `https://api.qrserver.com/v1/create-qr-code/` | Custom QR images, no auth |
- Tag with `@external-test-api` and replace before production
- Isolate use to non-production, clearly mark as temporary
- Do not leave blank `src=""`, broken links, or raw strings in UI mocks
- Placeholder dimensions must match final design
- Visual output must remain tidy and client-ready, even in prototypes
> Example:
```ts
/// Test: createUser – should log error and redact email on DB failure
expect(logger.error).toHaveBeenCalledWith(
  expect.objectContaining({
    requestId: expect.any(String),
    stack: expect.any(String),
    actor: expect.any(String),
    message: expect.stringContaining('Database error'),
    userEmail: '[REDACTED]'
  })
);
```
---
  - `https://jsonplaceholder.typicode.com/` — for mock object schemas and response scaffolding
- Use cases:
  - Placeholder assets (e.g. thumbnails, avatars)
  - Dummy text or test schema outputs
  - Early-stage UI layout or component validation
- Usage requirements:
  - Must be **isolated** to non-production environments
  - Must be clearly marked as temporary (e.g. in `README`, comments, or test metadata)
  - Tag with `@external-test-api` and/or `@placeholder-asset` in code
  - Replace before feature freeze or production deployment
> Example:  
> `GET https://placehold.co/600x400?text=Preview` – temporary hero image placeholder
- Quality enforcement:
  - Do **not** leave blank `src=""`, broken links, or raw strings in UI mocks
  - Placeholder dimensions must reflect final design to prevent layout distortion
  - Visual output must remain tidy and client-ready — even in prototype state
#### Approved Placeholder & Enrichment APIs
The following APIs are acceptable for non-production use in MVPs, demos, or test environments:
| Use Case | API | Notes |
|-|-|-|
| Image placeholders| `https://placehold.co/` | Clean, customisable images |
| Fake user data| `https://randomuser.me/` | Profiles, names, emails |
| JSON scaffolding| `https://jsonplaceholder.typicode.com/` | REST-style dummy endpoints |
| E-commerce mock data| `https://dummyjson.com/` | Products, carts, users |
| Placeholder text| `https://loripsum.net/` | Multi-paragraph lorem ipsum |
| Avatar generation| `https://avatars.dicebear.com/` | Seed-based avatars with multiple styles |
| Country & geo data| `https://restcountries.com/` | For dropdowns, analytics, or dashboard mocks |
| QR codes| `https://api.qrserver.com/v1/create-qr-code/` | Generate custom QR images without auth |
> **Tag usage:** Use `@external-test-api` and replace before production.
---
### 13.9 Test Fixtures
- Store test fixtures in `/fixtures` or `__mocks__`
- Use anonymised, synthetic data — never real user info
- Clear test data state between runs (`beforeEach` or transactional rollback)