# 🎯 Flexair Approval System - Complete Testing Information

## 🚀 Quick Start Guide

### 1. Start the Application
```bash
# Terminal 1 - Backend Server
cd backend
npm run dev
# Should show: Server running on port 5000

# Terminal 2 - Frontend Server  
cd frontend
npm run dev
# Should show: Ready on http://localhost:3000
```

### 2. Setup Test Data
```bash
# 

mysql -u your_username -p your_database < setup-test-environment.sql
```

### 3. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000/api
- **Test Endpoints**: Run `test-endpoints.bat` (Windows) or use curl commands

## 👥 Test User Accounts

| Role | Email | Password | Purpose |
|------|-------|----------|---------|
| Employee | <EMAIL> | password123 | Create approval requests |
| Manager | <EMAIL> | password123 | Approve/reject requests |
| Admin | <EMAIL> | password123 | Full system access |
| Employee 2 | <EMAIL> | password123 | Additional test data |

## 🎯 Key Testing Scenarios

### Scenario 1: Employee Workflow ⭐ **START HERE**
1. **Login**: http://localhost:3000/auth/login (<EMAIL>)
2. **Time Tracking**: http://localhost:3000/time-tracking
3. **Create Request**: Click "Request Approval" on any time log
4. **Fill Form**: 
   - Type: Correction
   - Clock In: 09:00
   - Clock Out: 17:30
   - Reason: "Forgot to clock in on time"
5. **Submit**: Click "Submit Request"
6. **Verify**: Check approvals page for pending request

### Scenario 2: Manager Workflow ⭐ **CRITICAL**
1. **Login**: http://localhost:3000/auth/login (<EMAIL>)
2. **Dashboard**: Check notification badges
3. **Approvals**: http://localhost:3000/approvals
4. **Review**: See pending requests from employees
5. **Process**: Click "Approve" or "Reject"
6. **Comments**: Add approval/rejection comments
7. **Verify**: Check request status updates

### Scenario 3: Notification System ⭐ **IMPORTANT**
1. **Employee**: Create multiple approval requests
2. **Manager**: Check dashboard for notification badges
3. **Real-time**: Verify counts update automatically
4. **Processing**: Approve requests and watch counts decrease

## 🔍 What to Look For

### ✅ Success Indicators
- [ ] No console errors in browser (F12 → Console)
- [ ] Forms submit successfully with success messages
- [ ] Status badges show correct colors (red=pending, green=approved, etc.)
- [ ] Notification counts update in real-time
- [ ] Manager sees different interface than employee
- [ ] Bulk operations work (select multiple → approve/reject)
- [ ] Filtering works (status, type, date range)
- [ ] Mobile responsive (test on phone/tablet)

### ❌ Failure Indicators
- Console errors or network failures
- Forms don't submit or show validation errors
- Status doesn't update after approval/rejection
- Notifications don't appear or update
- Manager can't see employee requests
- Database errors or data corruption

## 🛠️ Troubleshooting

### Backend Issues
```bash
# Check if backend is running
curl http://localhost:5000/api/health

# Check backend logs
# Look at terminal running "npm run dev" in backend folder
```

### Frontend Issues
```bash
# Check if frontend is running
curl http://localhost:3000

# Check browser console
# Press F12 → Console tab for errors
```

### Database Issues
```sql
-- Verify approval_requests table exists
DESCRIBE approval_requests;

-- Check test data
SELECT * FROM approval_requests ORDER BY created_at DESC LIMIT 5;

-- Verify user roles
SELECT email, role FROM users WHERE email LIKE '%flexair.com';
```

## 📊 Test Coverage Areas

### Core Functionality (Must Test)
- [x] Employee can create approval requests
- [x] Manager can approve/reject requests  
- [x] Status updates correctly
- [x] Notifications work
- [x] Comments are saved and displayed

### Advanced Features (Should Test)
- [x] Bulk operations (approve/reject multiple)
- [x] Filtering and search
- [x] Different request types (correction, overtime, leave)
- [x] Pagination for large datasets
- [x] Mobile responsiveness

### Edge Cases (Nice to Test)
- [x] Form validation (empty fields, invalid data)
- [x] Network error handling
- [x] Large datasets (50+ requests)
- [x] Concurrent user operations
- [x] Browser refresh during operations

## 🎉 Success Criteria

### Minimum Viable Product (MVP)
✅ **PASS**: Employee can create requests, manager can approve them, status updates correctly

### Full Feature Set
✅ **PASS**: All scenarios work + bulk operations + notifications + mobile responsive

### Production Ready
✅ **PASS**: All tests pass + no console errors + handles edge cases gracefully

## 📞 Getting Help

### Common Issues
1. **"No pending approvals"** → Check user roles in database
2. **"Request failed"** → Check backend server is running
3. **"Validation error"** → Check required fields are filled
4. **"Not authorized"** → Check user is logged in with correct role

### Debug Information
- **Backend Logs**: Terminal running backend server
- **Frontend Logs**: Browser console (F12)
- **Network Requests**: Browser Network tab (F12)
- **Database State**: Run SQL queries to check data

### Files to Check
- `backend/src/modules/approvals/` - Backend approval logic
- `frontend/src/components/approvals/` - Frontend components
- `frontend/src/lib/approvals.ts` - API integration
- `MANUAL_TESTING_GUIDE.md` - Detailed testing steps

## 🎯 Quick Validation Commands

```bash
# Check servers are running
netstat -an | findstr :3000  # Frontend
netstat -an | findstr :5000  # Backend

# Test API endpoints
curl http://localhost:5000/api/health
curl http://localhost:3000/api/health

# Check database connection
mysql -u root -p -e "SELECT COUNT(*) FROM approval_requests;"
```

## 📋 Testing Checklist

**Before Starting:**
- [ ] Backend server running on port 5000
- [ ] Frontend server running on port 3000  
- [ ] Test users created in database
- [ ] Sample time logs exist

**Core Tests:**
- [ ] Employee can login and create requests
- [ ] Manager can login and process requests
- [ ] Status updates correctly after processing
- [ ] Notifications show correct counts

**Advanced Tests:**
- [ ] Bulk operations work
- [ ] Filtering and pagination work
- [ ] Mobile interface is usable
- [ ] Error handling works properly

**Final Verification:**
- [ ] No console errors
- [ ] Database data is correct
- [ ] All user roles work as expected
- [ ] System handles concurrent users

---

## 🎉 You're Ready to Test!

The approval system is fully implemented and ready for comprehensive testing. Start with Scenario 1 (Employee Workflow) and work through each scenario systematically. The system should handle all operations smoothly with proper feedback and error handling.

**Happy Testing! 🚀**
