"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/time/page",{

/***/ "(app-pages-browser)/./src/components/time/TimeLogs.tsx":
/*!******************************************!*\
  !*** ./src/components/time/TimeLogs.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TimeLogs; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/approvals/ApprovalRequestForm */ \"(app-pages-browser)/./src/components/approvals/ApprovalRequestForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\r\n * Time Logs component for viewing and managing time entries\r\n * Displays paginated list of time logs with filtering options\r\n */ function TimeLogs() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [timeLogs, setTimeLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Filter states\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Approval request modal state\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTimeLog, setSelectedTimeLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize with current week\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const now = new Date();\n        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));\n        const weekEnd = new Date(weekStart);\n        weekEnd.setDate(weekStart.getDate() + 6);\n        setStartDate(weekStart.toISOString().split(\"T\")[0]);\n        setEndDate(weekEnd.toISOString().split(\"T\")[0]);\n    }, []);\n    // Load time logs when filters or page changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (startDate && endDate) {\n            loadTimeLogs();\n        }\n    }, [\n        currentPage,\n        startDate,\n        endDate,\n        status\n    ]);\n    /**\r\n   * Load time logs from API with current filters\r\n   */ const loadTimeLogs = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: \"10\",\n                start_date: startDate,\n                end_date: endDate,\n                ...status !== \"all\" && {\n                    status\n                }\n            });\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/logs?\".concat(params));\n            if (response.success && response.data) {\n                const data = response.data;\n                setTimeLogs(data.logs);\n                setTotalPages(data.totalPages);\n                setTotalCount(data.totalCount);\n            } else {\n                setError(response.message || \"Failed to load time logs\");\n            }\n        } catch (error) {\n            setError(\"Error loading time logs. Please try again.\");\n            console.error(\"Time logs error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Handle filter changes\r\n   */ const handleFilterChange = ()=>{\n        setCurrentPage(1); // Reset to first page when filters change\n        loadTimeLogs();\n    };\n    /**\r\n   * Handle page changes\r\n   */ const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    /**\r\n   * Format date for display\r\n   */ const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    /**\r\n   * Format time for display\r\n   */ const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    /**\r\n   * Get status badge color\r\n   */ const getStatusBadge = (status)=>{\n        const colors = {\n            active: \"bg-blue-100 text-blue-800\",\n            completed: \"bg-green-100 text-green-800\",\n            approved: \"bg-purple-100 text-purple-800\"\n        };\n        return colors[status] || \"bg-gray-100 text-gray-800\";\n    };\n    /**\r\n   * Calculate total hours for the period\r\n   */ const getTotalHours = ()=>{\n        return timeLogs.reduce((total, log)=>{\n            if (log.total_hours) {\n                const hours = parseFloat(log.total_hours);\n                return total + hours;\n            }\n            return total;\n        }, 0).toFixed(2);\n    };\n    /**\r\n   * Set quick date filters\r\n   */ const setQuickDateFilter = (days)=>{\n        const end = new Date();\n        const start = new Date();\n        start.setDate(end.getDate() - days);\n        setStartDate(start.toISOString().split(\"T\")[0]);\n        setEndDate(end.toISOString().split(\"T\")[0]);\n    };\n    /**\r\n   * Filter time logs based on search term\r\n   */ const filteredTimeLogs = timeLogs.filter((log)=>{\n        var _log_clock_in_location, _log_notes;\n        if (!searchTerm) return true;\n        const searchLower = searchTerm.toLowerCase();\n        return ((_log_clock_in_location = log.clock_in_location) === null || _log_clock_in_location === void 0 ? void 0 : _log_clock_in_location.toLowerCase().includes(searchLower)) || ((_log_notes = log.notes) === null || _log_notes === void 0 ? void 0 : _log_notes.toLowerCase().includes(searchLower)) || log.status.toLowerCase().includes(searchLower);\n    });\n    /**\r\n   * Open approval request modal\r\n   */ const openApprovalModal = (timeLog)=>{\n        setSelectedTimeLog(timeLog);\n        setShowApprovalModal(true);\n    };\n    /**\r\n   * Close approval request modal\r\n   */ const closeApprovalModal = ()=>{\n        setShowApprovalModal(false);\n        setSelectedTimeLog(null);\n    };\n    /**\r\n   * Handle approval request success\r\n   */ const handleApprovalSuccess = ()=>{\n        closeApprovalModal();\n        // Optionally refresh the time logs to show updated status\n        loadTimeLogs();\n    };\n    /**\r\n   * Check if time log can be corrected\r\n   */ const canRequestCorrection = (timeLog)=>{\n        // Only allow correction requests for completed time logs\n        return timeLog.status === \"completed\" && timeLog.clock_out_time;\n    };\n    /**\r\n   * Export time logs to CSV\r\n   */ const exportToCSV = ()=>{\n        const headers = [\n            \"Date\",\n            \"Clock In\",\n            \"Clock Out\",\n            \"Total Hours\",\n            \"Location\",\n            \"Status\",\n            \"Notes\"\n        ];\n        const csvData = filteredTimeLogs.map((log)=>[\n                formatDate(log.clock_in_time),\n                formatTime(log.clock_in_time),\n                log.clock_out_time ? formatTime(log.clock_out_time) : \"\",\n                log.total_hours || \"\",\n                log.clock_in_location || \"\",\n                log.status,\n                log.notes || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((field)=>'\"'.concat(field, '\"')).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"time-logs-\".concat(startDate, \"-to-\").concat(endDate, \".csv\");\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-bold text-gray-800 mb-1\",\n                                    children: \"Time Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"View and manage your time entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportToCSV,\n                                disabled: filteredTimeLogs.length === 0,\n                                className: \"btn btn-success\",\n                                children: \"\\uD83D\\uDCE5 Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-semibold text-gray-800 mb-2 lg:mb-0\",\n                                children: \"Search & Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(0),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(7),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 7 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(30),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 30 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(90),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 3 Months\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search by location, notes, or status...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"form-input pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"startDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Start Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"startDate\",\n                                        value: startDate,\n                                        onChange: (e)=>setStartDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"endDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"End Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"endDate\",\n                                        value: endDate,\n                                        onChange: (e)=>setEndDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"status\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"status\",\n                                        value: status,\n                                        onChange: (e)=>setStatus(e.target.value),\n                                        className: \"form-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"approved\",\n                                                children: \"Approved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleFilterChange,\n                                    disabled: isLoading,\n                                    className: \"btn btn-primary w-full\",\n                                    children: isLoading ? \"Loading...\" : \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            timeLogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-bold text-blue-800\",\n                                    children: totalCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Showing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-bold text-blue-800\",\n                                    children: filteredTimeLogs.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-bold text-blue-800\",\n                                    children: getTotalHours()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Date Range\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-semibold text-blue-800\",\n                                    children: [\n                                        formatDate(startDate),\n                                        \" - \",\n                                        formatDate(endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-200 text-red-700 px-3 py-2 rounded-lg mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading time logs...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this) : filteredTimeLogs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: timeLogs.length === 0 ? \"No time logs found for the selected period.\" : \"No time logs match your search criteria.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 418,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"table\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"table-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Clock In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Clock Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Total Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredTimeLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"table-row\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: formatDate(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: formatTime(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: log.clock_out_time ? formatTime(log.clock_out_time) : \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: log.total_hours || \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell text-gray-500\",\n                                            children: log.clock_in_location || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"badge \".concat(getStatusBadge(log.status)),\n                                                children: log.status.charAt(0).toUpperCase() + log.status.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: canRequestCorrection(log) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>openApprovalModal(log),\n                                                className: \"text-blue-600 hover:text-blue-900 transition duration-200\",\n                                                children: \"Request Correction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, log.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 426,\n                columnNumber: 9\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing page \",\n                            currentPage,\n                            \" of \",\n                            totalPages,\n                            \" (\",\n                            totalCount,\n                            \" total entries)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: Math.min(5, totalPages)\n                            }, (_, i)=>{\n                                const page = i + 1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: \"px-3 py-1 text-sm rounded-md transition duration-200 \".concat(currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 hover:bg-gray-300\"),\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, this),\n            showApprovalModal && selectedTimeLog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-4 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        timeLogId: selectedTimeLog.id,\n                        currentClockIn: selectedTimeLog.clock_in_time,\n                        currentClockOut: selectedTimeLog.clock_out_time || \"\",\n                        onSuccess: handleApprovalSuccess,\n                        onCancel: closeApprovalModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(TimeLogs, \"h+UOzgzavemObQz7KGM+r5yYl2A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = TimeLogs;\nvar _c;\n$RefreshReg$(_c, \"TimeLogs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/time/TimeLogs.tsx\n"));

/***/ })

});