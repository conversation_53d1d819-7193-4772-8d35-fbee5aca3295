## Security Requirements
> **Rule:** Apply layered, proactive security at all boundaries and interfaces.
**Input Validation**
- Validate all inputs: type, format, length, allowed values
- Prefer whitelists (enums, regex, schemas) over blacklists
- Reject null, empty, or ambiguous values early
- Validate headers, query params, and body fields equally
**Query Safety**
- Use parameterized queries or vetted ORM
- Avoid raw SQL unless reviewed and locked down
- Never interpolate variables into SQL, shell commands, or URLs
**Authentication & Authorization**
- Enforce both authN and authZ at every service boundary
- Authorize by role, scope, or resource ownership
- Deny by default — no fallbacks for unknown/missing scopes
- Log access decisions and token failures
**Data Protection**
- Encrypt in-flight (TLS 1.2+) and at-rest (AES-256+)
- Never store secrets or credentials in plaintext
- Redact/hash sensitive info in logs and telemetry
- Tokenise PII when feasible
**Rate Limiting & Abuse Protection**
- Apply per-user and per-IP rate limits
- Use timeouts on all external calls
- Apply exponential backoff on retries
- Monitor for abuse (e.g. brute-force, 401 floods)
**Secure Defaults**
- Disable debug routes and test creds in prod
- Enforce strong passwords, session expiry, and MFA
- Use signed, time-limited tokens with scoped access
**Resource Cleanup**
- Always release resources in `finally` blocks
- Cancel async/background tasks on failure or cancellation
**Secure Dependencies**
- Pin critical versions; check against Snyk/Dependabot
- Audit transitive dependencies regularly
- Avoid unmaintained or deprecated libraries, especially for auth, crypto, or file handling
> **Reminder:** If security behavior isn’t testable, it isn’t reliable. See §13.7 for validation practices.
