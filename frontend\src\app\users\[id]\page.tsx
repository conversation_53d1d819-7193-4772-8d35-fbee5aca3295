'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import UserProfile from '@/components/users/UserProfile';
import UserForm from '@/components/users/UserForm';
import { User, getUserById } from '@/lib/users';

/**
 * Individual user page
 * Displays user profile with edit capabilities
 */
function UserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = parseInt(params.id as string);

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [view, setView] = useState<'profile' | 'edit'>('profile');

  // Load user data on component mount
  useEffect(() => {
    if (userId) {
      loadUser();
    }
  }, [userId]);

  /**
   * Load user data from API
   */
  const loadUser = async () => {
    try {
      setLoading(true);
      setError('');
      const userData = await getUserById(userId);
      setUser(userData);
    } catch (err: any) {
      setError(err.message || 'Failed to load user');
      console.error('Error loading user:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle edit mode
   */
  const handleEdit = () => {
    setView('edit');
  };

  /**
   * Handle successful user update
   */
  const handleUserSuccess = (updatedUser: User) => {
    setUser(updatedUser);
    setView('profile');
  };

  /**
   * Handle cancel edit
   */
  const handleCancel = () => {
    setView('profile');
  };

  /**
   * Handle close/back navigation
   */
  const handleClose = () => {
    router.push('/users');
  };

  if (loading) {
    return (
      <ProtectedRoute requiredRoles={['admin', 'manager']}>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading user...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !user) {
    return (
      <ProtectedRoute requiredRoles={['admin', 'manager']}>
        <div className="min-h-screen bg-gray-50">
          <header className="bg-white shadow">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-6">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => router.push('/users')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">User Not Found</h1>
                    <p className="text-sm text-gray-600">The requested user could not be loaded</p>
                  </div>
                </div>
              </div>
            </div>
          </header>

          <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="bg-white rounded-lg shadow-md p-8 text-center">
              <div className="text-red-500 mb-4">
                <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">User Not Found</h2>
              <p className="text-gray-600 mb-6">
                {error || 'The user you are looking for does not exist or you do not have permission to view it.'}
              </p>
              <div className="space-x-4">
                <button
                  onClick={() => router.push('/users')}
                  className="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-md font-medium"
                >
                  Back to Users
                </button>
                <button
                  onClick={loadUser}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md font-medium"
                >
                  Try Again
                </button>
              </div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requiredRoles={['admin', 'manager']}>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => router.push('/users')}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    {view === 'profile' ? 'User Profile' : 'Edit User'}
                  </h1>
                  <p className="text-sm text-gray-600">
                    {view === 'profile' 
                      ? `${user.firstName} ${user.lastName} - ${user.email}`
                      : 'Update user information and settings'
                    }
                  </p>
                </div>
              </div>

              {/* Navigation breadcrumbs */}
              <nav className="flex items-center space-x-2 text-sm text-gray-500">
                <button
                  onClick={() => router.push('/dashboard')}
                  className="hover:text-gray-700"
                >
                  Dashboard
                </button>
                <span>/</span>
                <button
                  onClick={() => router.push('/users')}
                  className="hover:text-gray-700"
                >
                  Users
                </button>
                <span>/</span>
                <span className="text-gray-900 font-medium">
                  {user.firstName} {user.lastName}
                </span>
                {view === 'edit' && (
                  <>
                    <span>/</span>
                    <span className="text-gray-900 font-medium">Edit</span>
                  </>
                )}
              </nav>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {view === 'profile' && (
            <UserProfile
              user={user}
              onEdit={handleEdit}
              onClose={handleClose}
            />
          )}

          {view === 'edit' && (
            <UserForm
              user={user}
              mode="edit"
              onSuccess={handleUserSuccess}
              onCancel={handleCancel}
            />
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}

export default UserPage;
