"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/time/page",{

/***/ "(app-pages-browser)/./src/components/time/ClockInOut.tsx":
/*!********************************************!*\
  !*** ./src/components/time/ClockInOut.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ClockInOut; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\r\n * Clock In/Out component for time tracking\r\n * Allows employees to clock in/out and view current session info\r\n */ function ClockInOut() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [activeSession, setActiveSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeStats, setTimeStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDetectingLocation, setIsDetectingLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [coordinates, setCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Update current time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 1000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Load active session and stats on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadActiveSession();\n        loadTimeStats();\n    }, []);\n    // Note: Removed auto-detection to prevent interference with manual detection\n    // Users can manually click the GPS button when they want location detection\n    /**\r\n   * Load the current active session from API\r\n   */ const loadActiveSession = async ()=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/active-session\");\n            if (response.success && response.data) {\n                setActiveSession(response.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading active session:\", error);\n        }\n    };\n    /**\r\n   * Load time statistics from API\r\n   */ const loadTimeStats = async ()=>{\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/stats\");\n            if (response.success && response.data) {\n                setTimeStats(response.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading time stats:\", error);\n        }\n    };\n    /**\r\n   * Detect user's current location using GPS\r\n   */ const detectLocation = async ()=>{\n        setIsDetectingLocation(true);\n        setMessage(\"Getting your location...\");\n        // Clear previous coordinates to ensure fresh detection\n        setCoordinates(null);\n        setLocation(\"\");\n        try {\n            if (!navigator.geolocation) {\n                setMessage(\"Geolocation is not supported by this browser\");\n                return;\n            }\n            setMessage(\"Acquiring GPS coordinates...\");\n            const position = await getCurrentPosition();\n            const { latitude, longitude, accuracy } = position.coords;\n            console.log(\"GPS Position acquired:\", {\n                latitude,\n                longitude,\n                accuracy\n            });\n            setCoordinates({\n                lat: latitude,\n                lng: longitude\n            });\n            setMessage(\"Converting coordinates to address...\");\n            // Reverse geocode to get readable address\n            const address = await reverseGeocode(latitude, longitude);\n            setLocation(address);\n            if (accuracy > 100) {\n                setMessage(\"Location detected (accuracy: \".concat(Math.round(accuracy), \"m). You may want to try again for better accuracy.\"));\n            } else {\n                setMessage(\"Location detected successfully! (accuracy: \".concat(Math.round(accuracy), \"m)\"));\n            }\n        } catch (error) {\n            console.error(\"Location detection error:\", error);\n            setCoordinates(null); // Clear coordinates on error\n            if (error instanceof GeolocationPositionError) {\n                switch(error.code){\n                    case error.PERMISSION_DENIED:\n                        setMessage(\"Location access denied. Please enable location permissions in your browser and try again.\");\n                        break;\n                    case error.POSITION_UNAVAILABLE:\n                        setMessage(\"Location information unavailable. Please check your GPS/network and try again, or enter location manually.\");\n                        break;\n                    case error.TIMEOUT:\n                        setMessage(\"Location detection timed out. Please try again or enter location manually.\");\n                        break;\n                    default:\n                        setMessage(\"Location detection failed. Please enter location manually.\");\n                        break;\n                }\n            } else {\n                setMessage(\"Unable to detect location. Please enter location manually.\");\n            }\n        } finally{\n            setIsDetectingLocation(false);\n        }\n    };\n    /**\r\n   * Get current position as a Promise with improved accuracy\r\n   */ const getCurrentPosition = ()=>{\n        return new Promise((resolve, reject)=>{\n            navigator.geolocation.getCurrentPosition((position)=>{\n                // Validate position accuracy\n                if (position.coords.accuracy > 100) {\n                    console.warn(\"GPS accuracy is low:\", position.coords.accuracy, \"meters\");\n                // Still resolve but with warning\n                }\n                resolve(position);\n            }, reject, {\n                enableHighAccuracy: true,\n                timeout: 15000,\n                maximumAge: 60000 // Reduced cache time to 1 minute for fresher readings\n            });\n        });\n    };\n    /**\r\n   * Reverse geocode coordinates to readable address\r\n   */ const reverseGeocode = async (lat, lng)=>{\n        try {\n            console.log(\"Reverse geocoding coordinates:\", {\n                lat,\n                lng\n            });\n            // Using OpenStreetMap Nominatim API (free, no API key required)\n            const response = await fetch(\"https://nominatim.openstreetmap.org/reverse?format=json&lat=\".concat(lat, \"&lon=\").concat(lng, \"&zoom=18&addressdetails=1&limit=1\"), {\n                headers: {\n                    \"User-Agent\": \"FlexairTimekeeping/1.0\",\n                    \"Accept\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log(\"Geocoding response:\", data);\n            if (data && data.display_name) {\n                // Extract relevant parts of the address\n                const address = data.address || {};\n                const parts = [];\n                // Building number and street\n                if (address.house_number && address.road) {\n                    parts.push(\"\".concat(address.house_number, \" \").concat(address.road));\n                } else if (address.road) {\n                    parts.push(address.road);\n                } else if (address.pedestrian) {\n                    parts.push(address.pedestrian);\n                }\n                // Neighborhood/area\n                if (address.neighbourhood || address.suburb || address.quarter) {\n                    parts.push(address.neighbourhood || address.suburb || address.quarter);\n                }\n                // City/town\n                if (address.city || address.town || address.village || address.municipality) {\n                    parts.push(address.city || address.town || address.village || address.municipality);\n                }\n                // State/region\n                if (address.state || address.region) {\n                    parts.push(address.state || address.region);\n                }\n                // Country (only if international)\n                if (address.country && address.country !== \"United States\") {\n                    parts.push(address.country);\n                }\n                let formattedAddress = parts.length > 0 ? parts.join(\", \") : data.display_name;\n                // Clean up the address\n                formattedAddress = formattedAddress.replace(/,\\s*,/g, \",\").trim();\n                // Limit length to keep it reasonable\n                if (formattedAddress.length > 100) {\n                    formattedAddress = formattedAddress.substring(0, 97) + \"...\";\n                }\n                console.log(\"Formatted address:\", formattedAddress);\n                return formattedAddress;\n            }\n            throw new Error(\"No address data in response\");\n        } catch (error) {\n            console.error(\"Reverse geocoding error:\", error);\n            // Fallback to coordinates if geocoding fails\n            const fallbackAddress = \"GPS: \".concat(lat.toFixed(6), \", \").concat(lng.toFixed(6));\n            console.log(\"Using fallback address:\", fallbackAddress);\n            return fallbackAddress;\n        }\n    };\n    /**\r\n   * Handle clock in action\r\n   */ const handleClockIn = async ()=>{\n        if (!location.trim()) {\n            setMessage(\"Please detect your location or enter it manually\");\n            return;\n        }\n        setIsLoading(true);\n        setMessage(\"\");\n        try {\n            const requestData = {\n                location: location.trim()\n            };\n            // Include GPS coordinates if available\n            if (coordinates) {\n                requestData.coordinates = coordinates;\n            }\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/clock-in\", {\n                method: \"POST\",\n                body: JSON.stringify(requestData)\n            });\n            if (response.success) {\n                setMessage(\"Successfully clocked in!\");\n                setLocation(\"\");\n                await loadActiveSession();\n                await loadTimeStats();\n            } else {\n                setMessage(response.message || \"Failed to clock in\");\n            }\n        } catch (error) {\n            setMessage(\"Error clocking in. Please try again.\");\n            console.error(\"Clock in error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Handle clock out action\r\n   */ const handleClockOut = async ()=>{\n        setIsLoading(true);\n        setMessage(\"\");\n        try {\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/clock-out\", {\n                method: \"POST\",\n                body: JSON.stringify({})\n            });\n            if (response.success) {\n                setMessage(\"Successfully clocked out!\");\n                setActiveSession(null);\n                await loadTimeStats();\n            } else {\n                setMessage(response.message || \"Failed to clock out\");\n            }\n        } catch (error) {\n            setMessage(\"Error clocking out. Please try again.\");\n            console.error(\"Clock out error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Calculate session duration\r\n   */ const getSessionDuration = ()=>{\n        if (!activeSession) return \"0:00:00\";\n        const clockInTime = new Date(activeSession.clock_in_time);\n        const duration = currentTime.getTime() - clockInTime.getTime();\n        const hours = Math.floor(duration / (1000 * 60 * 60));\n        const minutes = Math.floor(duration % (1000 * 60 * 60) / (1000 * 60));\n        const seconds = Math.floor(duration % (1000 * 60) / 1000);\n        return \"\".concat(hours, \":\").concat(minutes.toString().padStart(2, \"0\"), \":\").concat(seconds.toString().padStart(2, \"0\"));\n    };\n    /**\r\n   * Format time for display\r\n   */ const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold text-gray-800 mb-2\",\n                        children: \"Time Clock\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Welcome back, \",\n                            user === null || user === void 0 ? void 0 : user.firstName,\n                            \"!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-mono text-gray-800 mt-2\",\n                        children: currentTime.toLocaleString(\"en-US\", {\n                            weekday: \"long\",\n                            year: \"numeric\",\n                            month: \"long\",\n                            day: \"numeric\",\n                            hour: \"2-digit\",\n                            minute: \"2-digit\",\n                            second: \"2-digit\",\n                            hour12: true\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this),\n            activeSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-semibold text-green-800\",\n                                children: \"Currently Clocked In\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-medium\",\n                                children: \"Active\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Clock In Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-800\",\n                                        children: formatTime(activeSession.clock_in_time)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Session Duration\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-mono text-lg font-bold text-green-800\",\n                                        children: getSessionDuration()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-800\",\n                                        children: activeSession.clock_in_location || \"Not specified\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3 mb-4\",\n                children: !activeSession ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"location\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Location\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"location\",\n                                                    value: location,\n                                                    onChange: (e)=>setLocation(e.target.value),\n                                                    placeholder: \"Click GPS to detect location or enter manually\",\n                                                    className: \"form-input flex-1\",\n                                                    disabled: isLoading || isDetectingLocation\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: detectLocation,\n                                                    disabled: isLoading || isDetectingLocation,\n                                                    className: \"btn btn-primary flex items-center gap-2\",\n                                                    title: \"Detect my location using GPS\",\n                                                    children: isDetectingLocation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: \"Detecting...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-4 h-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: \"2\",\n                                                                        d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                        lineNumber: 435,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: \"2\",\n                                                                        d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                        lineNumber: 436,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: \"GPS\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this),\n                                                (location || coordinates) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setLocation(\"\");\n                                                        setCoordinates(null);\n                                                        setMessage(\"\");\n                                                    },\n                                                    disabled: isLoading || isDetectingLocation,\n                                                    className: \"btn bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white\",\n                                                    title: \"Clear location\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M6 18L18 6M6 6l12 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        coordinates && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-gray-500 bg-gray-50 p-2 rounded border\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"w-3 h-3\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: \"2\",\n                                                                d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"GPS: \",\n                                                        coordinates.lat.toFixed(6),\n                                                        \", \",\n                                                        coordinates.lng.toFixed(6)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: detectLocation,\n                                                    disabled: isDetectingLocation,\n                                                    className: \"text-blue-600 hover:text-blue-800 disabled:text-gray-400\",\n                                                    title: \"Refresh location\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-3 h-3\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: \"2\",\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleClockIn,\n                            disabled: isLoading || isDetectingLocation,\n                            className: \"btn btn-success w-full font-bold\",\n                            children: isLoading ? \"Clocking In...\" : \"Clock In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClockOut,\n                    disabled: isLoading,\n                    className: \"btn btn-error w-full font-bold\",\n                    children: isLoading ? \"Clocking Out...\" : \"Clock Out\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 403,\n                columnNumber: 7\n            }, this),\n            message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 rounded-md mb-6 \".concat(message.includes(\"Successfully\") || message.includes(\"detected successfully\") ? \"bg-green-100 text-green-700 border border-green-200\" : message.includes(\"Please detect\") || message.includes(\"enter manually\") ? \"bg-yellow-100 text-yellow-700 border border-yellow-200\" : \"bg-red-100 text-red-700 border border-red-200\"),\n                children: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, this),\n            timeStats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t pt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-semibold text-gray-800 mb-3\",\n                        children: \"Time Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base font-bold text-gray-800\",\n                                        children: timeStats.today_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"This Week\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base font-bold text-gray-800\",\n                                        children: timeStats.week_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"This Month\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base font-bold text-gray-800\",\n                                        children: timeStats.month_hours\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-2 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Overtime (Week)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base font-bold text-orange-600\",\n                                        children: timeStats.overtime_week\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n                lineNumber: 515,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\ClockInOut.tsx\",\n        lineNumber: 352,\n        columnNumber: 5\n    }, this);\n}\n_s(ClockInOut, \"/mL5Ih/TYUjlQDfVLVwrZeJ5LJQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = ClockInOut;\nvar _c;\n$RefreshReg$(_c, \"ClockInOut\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/time/ClockInOut.tsx\n"));

/***/ })

});