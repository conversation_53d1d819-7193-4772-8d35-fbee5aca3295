/**
 * Test the approval service directly
 */

require('dotenv').config({ path: './backend/.env' });

async function testApprovalService() {
  try {
    console.log('🧪 Testing approval service...\n');

    // Test database connection first
    const { query } = require('./backend/src/shared/database');
    
    console.log('1. Testing database connection...');
    const testQuery = await query('SELECT 1 as test');
    console.log('✅ Database connection successful:', testQuery);

    console.log('\n2. Testing approvals table structure...');
    const structure = await query('DESCRIBE approvals');
    console.log('✅ Approvals table structure:');
    console.table(structure);

    console.log('\n3. Testing approval service functions...');
    const approvalService = require('./backend/src/modules/approvals/approvalService');
    
    // Test getApprovals
    console.log('Testing getApprovals...');
    const approvals = await approvalService.getApprovals({}, 1, 5);
    console.log('✅ getApprovals successful:', {
      count: approvals.approvals.length,
      pagination: approvals.pagination
    });

    // Test getApprovalStats
    console.log('\nTesting getApprovalStats...');
    const stats = await approvalService.getApprovalStats();
    console.log('✅ getApprovalStats successful:', stats);

    console.log('\n🎉 All approval service tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testApprovalService();
