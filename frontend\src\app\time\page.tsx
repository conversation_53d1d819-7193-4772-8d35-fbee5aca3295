'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import ClockInOut from '@/components/time/ClockInOut';
import TimeLogs from '@/components/time/TimeLogs';

/**
 * Time Tracking page - main interface for employee time management
 * Combines clock in/out functionality with time logs viewing
 */
function TimeTrackingPage() {
  const [activeTab, setActiveTab] = useState<'clock' | 'logs'>('clock');

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-100 py-6">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="mb-6">
            <h1 className="text-xl font-bold text-gray-900">Time Tracking</h1>
            <p className="mt-1 text-gray-600">
              Manage your work hours, clock in/out, and view your time logs
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="mb-6">
            <nav className="flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('clock')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'clock'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Time Clock
              </button>
              <button
                onClick={() => setActiveTab('logs')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'logs'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Time Logs
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="transition-all duration-300">
            {activeTab === 'clock' && <ClockInOut />}
            {activeTab === 'logs' && <TimeLogs />}
          </div>

          {/* Quick Actions Footer */}
          <div className="mt-6 compact-card">
            <h3 className="text-base font-semibold text-gray-800 mb-3">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <button
                onClick={() => setActiveTab('clock')}
                className="p-2 text-left bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg transition duration-200"
              >
                <div className="flex items-center">
                  <div className="icon-container-sm bg-blue-600 mr-2">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-blue-900">Time Clock</h4>
                    <p className="text-sm text-blue-600">Clock in or out of your shift</p>
                  </div>
                </div>
              </button>

              <button
                onClick={() => setActiveTab('logs')}
                className="p-2 text-left bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg transition duration-200"
              >
                <div className="flex items-center">
                  <div className="icon-container-sm bg-green-600 mr-2">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-green-900">View Time Logs</h4>
                    <p className="text-sm text-green-600">Review your time entries and hours</p>
                  </div>
                </div>
              </button>

              <a
                href="/dashboard"
                className="p-2 text-left bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg transition duration-200"
              >
                <div className="flex items-center">
                  <div className="icon-container-sm bg-purple-600 mr-2">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-purple-900">Dashboard</h4>
                    <p className="text-sm text-purple-600">View summary and reports</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default TimeTrackingPage;