'use client';

import React, { useState, useEffect } from 'react';
import { 
  getPendingApprovals, 
  processApproval, 
  ApprovalRequest, 
  ApprovalFilters,
  getStatusBadgeColor,
  getRequestTypeDisplayName,
  formatRequestData
} from '@/lib/approvals';

/**
 * Props for ManagerApprovalInterface component
 */
interface ManagerApprovalInterfaceProps {
  onApprovalProcessed?: () => void;
}

/**
 * Process approval modal data
 */
interface ProcessModalData {
  approval: ApprovalRequest;
  action: 'approve' | 'reject';
}

/**
 * ManagerApprovalInterface Component
 * Interface for managers to view, approve, and reject pending approval requests
 */
export default function ManagerApprovalInterface({ onApprovalProcessed }: ManagerApprovalInterfaceProps) {
  const [approvals, setApprovals] = useState<ApprovalRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedApprovals, setSelectedApprovals] = useState<Set<number>>(new Set());
  const [processModal, setProcessModal] = useState<ProcessModalData | null>(null);
  const [comments, setComments] = useState('');
  const [processing, setProcessing] = useState(false);
  
  // Filters
  const [filters, setFilters] = useState<ApprovalFilters>({
    page: 1,
    limit: 20,
    status: 'pending'
  });

  /**
   * Load pending approvals
   */
  const loadApprovals = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await getPendingApprovals(filters);
      setApprovals(result.requests);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load approvals');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load approvals on component mount and filter changes
   */
  useEffect(() => {
    loadApprovals();
  }, [filters]);

  /**
   * Handle filter changes
   */
  const handleFilterChange = (key: keyof ApprovalFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  /**
   * Handle individual approval selection
   */
  const handleApprovalSelect = (approvalId: number) => {
    setSelectedApprovals(prev => {
      const newSet = new Set(prev);
      if (newSet.has(approvalId)) {
        newSet.delete(approvalId);
      } else {
        newSet.add(approvalId);
      }
      return newSet;
    });
  };

  /**
   * Handle select all approvals
   */
  const handleSelectAll = () => {
    if (selectedApprovals.size === approvals.length) {
      setSelectedApprovals(new Set());
    } else {
      setSelectedApprovals(new Set(approvals.map(a => a.id)));
    }
  };

  /**
   * Open process modal
   */
  const openProcessModal = (approval: ApprovalRequest, action: 'approve' | 'reject') => {
    setProcessModal({ approval, action });
    setComments('');
  };

  /**
   * Close process modal
   */
  const closeProcessModal = () => {
    setProcessModal(null);
    setComments('');
  };

  /**
   * Process single approval
   */
  const handleProcessApproval = async () => {
    if (!processModal) return;

    setProcessing(true);
    try {
      await processApproval(processModal.approval.id, {
        status: processModal.action === 'approve' ? 'approved' : 'rejected',
        comments: comments.trim() || undefined
      });

      // Refresh the list
      await loadApprovals();
      
      // Clear selection
      setSelectedApprovals(new Set());
      
      // Close modal
      closeProcessModal();
      
      if (onApprovalProcessed) {
        onApprovalProcessed();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process approval');
    } finally {
      setProcessing(false);
    }
  };

  /**
   * Process bulk approvals
   */
  const handleBulkProcess = async (action: 'approve' | 'reject') => {
    if (selectedApprovals.size === 0) return;

    setProcessing(true);
    try {
      const promises = Array.from(selectedApprovals).map(id =>
        processApproval(id, {
          status: action === 'approve' ? 'approved' : 'rejected',
          comments: `Bulk ${action}d by manager`
        })
      );

      await Promise.all(promises);

      // Refresh the list
      await loadApprovals();
      
      // Clear selection
      setSelectedApprovals(new Set());
      
      if (onApprovalProcessed) {
        onApprovalProcessed();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process bulk approvals');
    } finally {
      setProcessing(false);
    }
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">
            Pending Approvals ({approvals.length})
          </h2>
          
          {/* Bulk Actions */}
          {selectedApprovals.size > 0 && (
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkProcess('approve')}
                disabled={processing}
                className="px-3 py-1 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                Approve ({selectedApprovals.size})
              </button>
              <button
                onClick={() => handleBulkProcess('reject')}
                disabled={processing}
                className="px-3 py-1 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                Reject ({selectedApprovals.size})
              </button>
            </div>
          )}
        </div>

        {/* Filters */}
        <div className="mt-4 flex flex-wrap gap-4">
          <select
            value={filters.requestType || ''}
            onChange={(e) => handleFilterChange('requestType', e.target.value || undefined)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="">All Types</option>
            <option value="correction">Time Correction</option>
            <option value="overtime">Overtime</option>
            <option value="leave">Leave</option>
          </select>
          
          <input
            type="date"
            value={filters.startDate || ''}
            onChange={(e) => handleFilterChange('startDate', e.target.value || undefined)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            placeholder="Start Date"
          />
          
          <input
            type="date"
            value={filters.endDate || ''}
            onChange={(e) => handleFilterChange('endDate', e.target.value || undefined)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            placeholder="End Date"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Approvals List */}
      <div className="overflow-x-auto">
        {approvals.length === 0 ? (
          <div className="px-6 py-8 text-center text-gray-500">
            No pending approvals found.
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedApprovals.size === approvals.length && approvals.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reason
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Submitted
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {approvals.map((approval) => (
                <tr key={approval.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedApprovals.has(approval.id)}
                      onChange={() => handleApprovalSelect(approval.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {approval.requesterName || 'Unknown'}
                    </div>
                    <div className="text-sm text-gray-500">
                      {approval.requesterEmail}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor('pending')}`}>
                      {getRequestTypeDisplayName(approval.requestType)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {formatRequestData(approval.requestType, approval.requestData)}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {approval.reason}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(approval.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                    <button
                      onClick={() => openProcessModal(approval, 'approve')}
                      className="text-green-600 hover:text-green-900"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => openProcessModal(approval, 'reject')}
                      className="text-red-600 hover:text-red-900"
                    >
                      Reject
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Process Modal */}
      {processModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {processModal.action === 'approve' ? 'Approve' : 'Reject'} Request
              </h3>
              
              <div className="mb-4 p-3 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">
                  <strong>Employee:</strong> {processModal.approval.requesterName}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Type:</strong> {getRequestTypeDisplayName(processModal.approval.requestType)}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Reason:</strong> {processModal.approval.reason}
                </p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Comments (Optional)
                </label>
                <textarea
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any comments..."
                />
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={closeProcessModal}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleProcessApproval}
                  disabled={processing}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 ${
                    processModal.action === 'approve'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {processing ? 'Processing...' : (processModal.action === 'approve' ? 'Approve' : 'Reject')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
