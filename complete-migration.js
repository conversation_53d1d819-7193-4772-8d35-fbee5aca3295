/**
 * Complete the approvals table migration
 */

const mysql = require('mysql2/promise');
require('dotenv').config({ path: './backend/.env' });

async function completeMigration() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'flexair_timekeeping'
    });

    console.log('📊 Checking current table structure...');
    const [currentStructure] = await connection.execute('DESCRIBE approvals');
    console.table(currentStructure);

    const columnNames = currentStructure.map(col => col.Field);
    
    // Add missing columns
    if (!columnNames.includes('request_data')) {
      console.log('➕ Adding request_data column...');
      await connection.execute('ALTER TABLE approvals ADD COLUMN request_data TEXT AFTER request_type');
    }

    if (!columnNames.includes('reason')) {
      console.log('➕ Adding reason column...');
      await connection.execute('ALTER TABLE approvals ADD COLUMN reason TEXT NOT NULL DEFAULT "" AFTER request_data');
    }

    if (!columnNames.includes('approved_by')) {
      console.log('➕ Adding approved_by column...');
      await connection.execute('ALTER TABLE approvals ADD COLUMN approved_by INT NULL AFTER comments');
    }

    // Migrate existing data
    console.log('🔄 Migrating existing data...');
    const [existingData] = await connection.execute('SELECT * FROM approvals WHERE requested_by = 0 OR assigned_to = 0');
    
    for (const record of existingData) {
      await connection.execute(`
        UPDATE approvals 
        SET 
          requested_by = ?,
          assigned_to = ?,
          reason = COALESCE(NULLIF(reason, ''), comments, 'Legacy approval request'),
          approved_by = CASE WHEN status != 'pending' THEN ? ELSE NULL END
        WHERE id = ?
      `, [record.user_id, record.manager_id, record.manager_id, record.id]);
    }

    // Add foreign key constraints
    console.log('🔗 Adding foreign key constraints...');
    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_requested_by FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_assigned_to FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    // Add indexes
    console.log('📇 Adding indexes...');
    try {
      await connection.execute('CREATE INDEX idx_requested_by ON approvals (requested_by)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_assigned_to ON approvals (assigned_to)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_approved_by ON approvals (approved_by)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_request_type ON approvals (request_type)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    console.log('📊 Final table structure:');
    const [finalStructure] = await connection.execute('DESCRIBE approvals');
    console.table(finalStructure);

    console.log('📋 Sample data after migration:');
    const [sampleData] = await connection.execute('SELECT id, time_log_id, requested_by, assigned_to, request_type, reason, status FROM approvals LIMIT 3');
    console.table(sampleData);

    console.log('✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run migration
completeMigration();
