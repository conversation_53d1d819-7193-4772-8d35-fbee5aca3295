"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/users/page",{

/***/ "(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx":
/*!************************************************!*\
  !*** ./src/components/auth/ProtectedRoute.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ProtectedRoute; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/**\r\n * Protected Route Component\r\n * Wraps components that require authentication\r\n */ /* __next_internal_client_entry_do_not_use__ default,withAuth auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\r\n * Protected route component that handles authentication and authorization\r\n * @param children - Components to render if user is authorized\r\n * @param requireAuth - Whether authentication is required (default: true)\r\n * @param requiredRole - Minimum role required to access the route\r\n * @param redirectTo - URL to redirect to if not authorized\r\n * @param fallback - Component to show while loading\r\n */ function ProtectedRoute(param) {\n    let { children, requireAuth = true, requiredRole, redirectTo = \"/auth/login\", fallback } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading, isAuthenticated } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (loading) return; // Wait for auth state to be determined\n        // If authentication is required but user is not authenticated\n        if (requireAuth && !isAuthenticated) {\n            router.push(redirectTo);\n            return;\n        }\n        // If specific role is required, check user role\n        if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n            router.push(\"/unauthorized\");\n            return;\n        }\n    }, [\n        loading,\n        isAuthenticated,\n        user,\n        requireAuth,\n        requiredRole,\n        router,\n        redirectTo\n    ]);\n    /**\r\n   * Check if user has required role\r\n   * @param userRole - Current user's role\r\n   * @param requiredRole - Required role\r\n   * @returns True if user has sufficient role\r\n   */ const hasRequiredRole = (userRole, requiredRole)=>{\n        const roleHierarchy = {\n            admin: 3,\n            manager: 2,\n            employee: 1\n        };\n        return roleHierarchy[userRole] >= roleHierarchy[requiredRole];\n    };\n    // Show loading state\n    if (loading) {\n        return fallback || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 75,\n            columnNumber: 24\n        }, this);\n    }\n    // Show unauthorized if auth is required but user is not authenticated\n    if (requireAuth && !isAuthenticated) {\n        return null; // Router will handle redirect\n    }\n    // Show unauthorized if role is required but user doesn't have it\n    if (requiredRole && user && !hasRequiredRole(user.role, requiredRole)) {\n        return null; // Router will handle redirect\n    }\n    // Render children if all checks pass\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(ProtectedRoute, \"ooyx4hya8juwD5EnCVJ7xU83ckg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProtectedRoute;\n/**\r\n * Loading spinner component\r\n */ function LoadingSpinner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-6 w-6 text-indigo-600\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-2 text-sm text-gray-600\",\n                    children: \"Loading...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_c1 = LoadingSpinner;\n/**\r\n * Higher-order component for protecting pages\r\n * @param Component - Component to protect\r\n * @param options - Protection options\r\n * @returns Protected component\r\n */ function withAuth(Component) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const ProtectedComponent = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProtectedRoute, {\n            ...options,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.tsx\",\n            lineNumber: 136,\n            columnNumber: 5\n        }, this);\n    ProtectedComponent.displayName = \"withAuth(\".concat(Component.displayName || Component.name, \")\");\n    return ProtectedComponent;\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c1, \"LoadingSpinner\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/ProtectedRoute.tsx\n"));

/***/ })

});