"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/approvals/page",{

/***/ "(app-pages-browser)/./src/components/approvals/ApprovalHistory.tsx":
/*!******************************************************!*\
  !*** ./src/components/approvals/ApprovalHistory.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApprovalHistory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(app-pages-browser)/./src/lib/approvals.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * ApprovalHistory Component\n * Displays approval request history with status tracking and filtering\n */ function ApprovalHistory(param) {\n    let { viewMode = \"my-requests\", showFilters = true } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [approvals, setApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Filters\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    /**\n   * Load approvals based on view mode\n   */ const loadApprovals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            let result;\n            if (viewMode === \"my-requests\") {\n                result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getMyApprovalRequests)(filters);\n            } else {\n                result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getApprovals)(filters);\n            }\n            setApprovals(result.requests);\n            setPagination(result.pagination);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load approval history\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load approvals on component mount and filter changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApprovals();\n    }, [\n        filters,\n        viewMode\n    ]);\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1 // Reset to first page when filters change\n            }));\n    };\n    /**\n   * Handle pagination\n   */ const handlePageChange = (newPage)=>{\n        setFilters((prev)=>({\n                ...prev,\n                page: newPage\n            }));\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    /**\n   * Get status icon\n   */ const getStatusIcon = (status)=>{\n        switch(status){\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-yellow-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 11\n                }, this);\n            case \"approved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-green-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this);\n            case \"rejected\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5 text-red-500\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: viewMode === \"my-requests\" ? \"My Approval Requests\" : \"All Approval Requests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.status || \"\",\n                                onChange: (e)=>handleFilterChange(\"status\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"pending\",\n                                        children: \"Pending\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"approved\",\n                                        children: \"Approved\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"rejected\",\n                                        children: \"Rejected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.requestType || \"\",\n                                onChange: (e)=>handleFilterChange(\"requestType\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.startDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"startDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"Start Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.endDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"endDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"End Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: approvals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No approval requests found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Request Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Reason\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this),\n                                    viewMode === \"all-approvals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Requester\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Submitted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Processed\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Comments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: approvals.map((approval)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    getStatusIcon(approval.status),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getStatusBadgeColor)(approval.status)),\n                                                        children: approval.status.charAt(0).toUpperCase() + approval.status.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(approval.requestType)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.formatRequestData)(approval.requestType, approval.requestData)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                title: approval.reason,\n                                                children: approval.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 19\n                                        }, this),\n                                        viewMode === \"all-approvals\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: approval.requesterName || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: approval.requesterEmail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: formatDate(approval.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: approval.approvedAt ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: formatDate(approval.approvedAt)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    approval.approverName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: [\n                                                            \"by \",\n                                                            approval.approverName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 23\n                                            }, this) : \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                title: approval.comments,\n                                                children: approval.comments || \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, approval.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            pagination && pagination.totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200 flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing \",\n                            (pagination.page - 1) * pagination.limit + 1,\n                            \" to\",\n                            \" \",\n                            Math.min(pagination.page * pagination.limit, pagination.total),\n                            \" of\",\n                            \" \",\n                            pagination.total,\n                            \" results\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page - 1),\n                                disabled: pagination.page <= 1,\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700\",\n                                children: [\n                                    \"Page \",\n                                    pagination.page,\n                                    \" of \",\n                                    pagination.totalPages\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(pagination.page + 1),\n                                disabled: pagination.page >= pagination.totalPages,\n                                className: \"px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalHistory.tsx\",\n        lineNumber: 145,\n        columnNumber: 5\n    }, this);\n}\n_s(ApprovalHistory, \"VWoiX4U/biIqITl5CNiMR43F1zU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ApprovalHistory;\nvar _c;\n$RefreshReg$(_c, \"ApprovalHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FwcHJvdmFscy9BcHByb3ZhbEhpc3RvcnkudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1EO0FBUzFCO0FBQ3dCO0FBVWpEOzs7Q0FHQyxHQUNjLFNBQVNTLGdCQUFnQixLQUdqQjtRQUhpQixFQUN0Q0MsV0FBVyxhQUFhLEVBQ3hCQyxjQUFjLElBQUksRUFDRyxHQUhpQjs7SUFJdEMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR0osOERBQU9BO0lBQ3hCLE1BQU0sQ0FBQ0ssV0FBV0MsYUFBYSxHQUFHYiwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUNoRSxNQUFNLENBQUNjLFNBQVNDLFdBQVcsR0FBR2YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZ0IsT0FBT0MsU0FBUyxHQUFHakIsK0NBQVFBLENBQWdCO0lBQ2xELE1BQU0sQ0FBQ2tCLFlBQVlDLGNBQWMsR0FBR25CLCtDQUFRQSxDQUFNO0lBRWxELFVBQVU7SUFDVixNQUFNLENBQUNvQixTQUFTQyxXQUFXLEdBQUdyQiwrQ0FBUUEsQ0FBa0I7UUFDdERzQixNQUFNO1FBQ05DLE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRlQsV0FBVztZQUNYRSxTQUFTO1lBRVQsSUFBSVE7WUFDSixJQUFJaEIsYUFBYSxlQUFlO2dCQUM5QmdCLFNBQVMsTUFBTXRCLHFFQUFxQkEsQ0FBQ2lCO1lBQ3ZDLE9BQU87Z0JBQ0xLLFNBQVMsTUFBTXZCLDREQUFZQSxDQUFDa0I7WUFDOUI7WUFFQVAsYUFBYVksT0FBT0MsUUFBUTtZQUM1QlAsY0FBY00sT0FBT1AsVUFBVTtRQUNqQyxFQUFFLE9BQU9TLEtBQUs7WUFDWlYsU0FBU1UsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUmQsV0FBVztRQUNiO0lBQ0Y7SUFFQTs7R0FFQyxHQUNEZCxnREFBU0EsQ0FBQztRQUNSdUI7SUFDRixHQUFHO1FBQUNKO1FBQVNYO0tBQVM7SUFFdEI7O0dBRUMsR0FDRCxNQUFNcUIscUJBQXFCLENBQUNDLEtBQTRCQztRQUN0RFgsV0FBV1ksQ0FBQUEsT0FBUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRixJQUFJLEVBQUVDO2dCQUNQVixNQUFNLEVBQUUsMENBQTBDO1lBQ3BEO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1ZLG1CQUFtQixDQUFDQztRQUN4QmQsV0FBV1ksQ0FBQUEsT0FBUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUFgsTUFBTWE7WUFDUjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsZ0JBQWdCLENBQUNDO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFDSCxxQkFDRSw4REFBQ0M7b0JBQUlDLFdBQVU7b0JBQTBCQyxNQUFLO29CQUFlQyxTQUFROzhCQUNuRSw0RUFBQ0M7d0JBQUtDLFVBQVM7d0JBQVVDLEdBQUU7d0JBQXFIQyxVQUFTOzs7Ozs7Ozs7OztZQUcvSixLQUFLO2dCQUNILHFCQUNFLDhEQUFDUDtvQkFBSUMsV0FBVTtvQkFBeUJDLE1BQUs7b0JBQWVDLFNBQVE7OEJBQ2xFLDRFQUFDQzt3QkFBS0MsVUFBUzt3QkFBVUMsR0FBRTt3QkFBd0lDLFVBQVM7Ozs7Ozs7Ozs7O1lBR2xMLEtBQUs7Z0JBQ0gscUJBQ0UsOERBQUNQO29CQUFJQyxXQUFVO29CQUF1QkMsTUFBSztvQkFBZUMsU0FBUTs4QkFDaEUsNEVBQUNDO3dCQUFLQyxVQUFTO3dCQUFVQyxHQUFFO3dCQUEwTkMsVUFBUzs7Ozs7Ozs7Ozs7WUFHcFE7Z0JBQ0UsT0FBTztRQUNYO0lBQ0Y7SUFFQSxJQUFJeEMsU0FBUztRQUNYLHFCQUNFLDhEQUFDeUM7WUFBSVAsV0FBVTtzQkFDYiw0RUFBQ087Z0JBQUlQLFdBQVU7Ozs7Ozs7Ozs7O0lBR3JCO0lBRUEscUJBQ0UsOERBQUNPO1FBQUlQLFdBQVU7OzBCQUViLDhEQUFDTztnQkFBSVAsV0FBVTs7a0NBQ2IsOERBQUNRO3dCQUFHUixXQUFVO2tDQUNYdkMsYUFBYSxnQkFBZ0IseUJBQXlCOzs7Ozs7b0JBSXhEQyw2QkFDQyw4REFBQzZDO3dCQUFJUCxXQUFVOzswQ0FDYiw4REFBQ1M7Z0NBQ0N6QixPQUFPWixRQUFRMEIsTUFBTSxJQUFJO2dDQUN6QlksVUFBVSxDQUFDQyxJQUFNN0IsbUJBQW1CLFVBQVU2QixFQUFFQyxNQUFNLENBQUM1QixLQUFLLElBQUk2QjtnQ0FDaEViLFdBQVU7O2tEQUVWLDhEQUFDYzt3Q0FBTzlCLE9BQU07a0RBQUc7Ozs7OztrREFDakIsOERBQUM4Qjt3Q0FBTzlCLE9BQU07a0RBQVU7Ozs7OztrREFDeEIsOERBQUM4Qjt3Q0FBTzlCLE9BQU07a0RBQVc7Ozs7OztrREFDekIsOERBQUM4Qjt3Q0FBTzlCLE9BQU07a0RBQVc7Ozs7Ozs7Ozs7OzswQ0FHM0IsOERBQUN5QjtnQ0FDQ3pCLE9BQU9aLFFBQVEyQyxXQUFXLElBQUk7Z0NBQzlCTCxVQUFVLENBQUNDLElBQU03QixtQkFBbUIsZUFBZTZCLEVBQUVDLE1BQU0sQ0FBQzVCLEtBQUssSUFBSTZCO2dDQUNyRWIsV0FBVTs7a0RBRVYsOERBQUNjO3dDQUFPOUIsT0FBTTtrREFBRzs7Ozs7O2tEQUNqQiw4REFBQzhCO3dDQUFPOUIsT0FBTTtrREFBYTs7Ozs7O2tEQUMzQiw4REFBQzhCO3dDQUFPOUIsT0FBTTtrREFBVzs7Ozs7O2tEQUN6Qiw4REFBQzhCO3dDQUFPOUIsT0FBTTtrREFBUTs7Ozs7Ozs7Ozs7OzBDQUd4Qiw4REFBQ2dDO2dDQUNDQyxNQUFLO2dDQUNMakMsT0FBT1osUUFBUThDLFNBQVMsSUFBSTtnQ0FDNUJSLFVBQVUsQ0FBQ0MsSUFBTTdCLG1CQUFtQixhQUFhNkIsRUFBRUMsTUFBTSxDQUFDNUIsS0FBSyxJQUFJNkI7Z0NBQ25FYixXQUFVO2dDQUNWbUIsYUFBWTs7Ozs7OzBDQUdkLDhEQUFDSDtnQ0FDQ0MsTUFBSztnQ0FDTGpDLE9BQU9aLFFBQVFnRCxPQUFPLElBQUk7Z0NBQzFCVixVQUFVLENBQUNDLElBQU03QixtQkFBbUIsV0FBVzZCLEVBQUVDLE1BQU0sQ0FBQzVCLEtBQUssSUFBSTZCO2dDQUNqRWIsV0FBVTtnQ0FDVm1CLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU9uQm5ELHVCQUNDLDhEQUFDdUM7Z0JBQUlQLFdBQVU7MEJBQ2IsNEVBQUNxQjtvQkFBRXJCLFdBQVU7OEJBQXdCaEM7Ozs7Ozs7Ozs7OzBCQUt6Qyw4REFBQ3VDO2dCQUFJUCxXQUFVOzBCQUNacEMsVUFBVTBELE1BQU0sS0FBSyxrQkFDcEIsOERBQUNmO29CQUFJUCxXQUFVOzhCQUFzQzs7Ozs7eUNBSXJELDhEQUFDdUI7b0JBQU12QixXQUFVOztzQ0FDZiw4REFBQ3dCOzRCQUFNeEIsV0FBVTtzQ0FDZiw0RUFBQ3lCOztrREFDQyw4REFBQ0M7d0NBQUcxQixXQUFVO2tEQUFpRjs7Ozs7O2tEQUcvRiw4REFBQzBCO3dDQUFHMUIsV0FBVTtrREFBaUY7Ozs7OztrREFHL0YsOERBQUMwQjt3Q0FBRzFCLFdBQVU7a0RBQWlGOzs7Ozs7a0RBRy9GLDhEQUFDMEI7d0NBQUcxQixXQUFVO2tEQUFpRjs7Ozs7O29DQUc5RnZDLGFBQWEsaUNBQ1osOERBQUNpRTt3Q0FBRzFCLFdBQVU7a0RBQWlGOzs7Ozs7a0RBSWpHLDhEQUFDMEI7d0NBQUcxQixXQUFVO2tEQUFpRjs7Ozs7O2tEQUcvRiw4REFBQzBCO3dDQUFHMUIsV0FBVTtrREFBaUY7Ozs7OztrREFHL0YsOERBQUMwQjt3Q0FBRzFCLFdBQVU7a0RBQWlGOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLbkcsOERBQUMyQjs0QkFBTTNCLFdBQVU7c0NBQ2RwQyxVQUFVZ0UsR0FBRyxDQUFDLENBQUNDLHlCQUNkLDhEQUFDSjtvQ0FBcUJ6QixXQUFVOztzREFDOUIsOERBQUM4Qjs0Q0FBRzlCLFdBQVU7c0RBQ1osNEVBQUNPO2dEQUFJUCxXQUFVOztvREFDWkgsY0FBY2dDLFNBQVMvQixNQUFNO2tFQUM5Qiw4REFBQ2lDO3dEQUFLL0IsV0FBVyxpRUFBc0csT0FBckM1QyxtRUFBbUJBLENBQUN5RSxTQUFTL0IsTUFBTTtrRUFDbEgrQixTQUFTL0IsTUFBTSxDQUFDa0MsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0osU0FBUy9CLE1BQU0sQ0FBQ29DLEtBQUssQ0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSXZFLDhEQUFDSjs0Q0FBRzlCLFdBQVU7c0RBQ1osNEVBQUMrQjtnREFBSy9CLFdBQVU7MERBQ2IzQyx5RUFBeUJBLENBQUN3RSxTQUFTZCxXQUFXOzs7Ozs7Ozs7OztzREFHbkQsOERBQUNlOzRDQUFHOUIsV0FBVTtzREFDWiw0RUFBQ087Z0RBQUlQLFdBQVU7MERBQ1oxQyxpRUFBaUJBLENBQUN1RSxTQUFTZCxXQUFXLEVBQUVjLFNBQVNNLFdBQVc7Ozs7Ozs7Ozs7O3NEQUdqRSw4REFBQ0w7NENBQUc5QixXQUFVO3NEQUNaLDRFQUFDTztnREFBSVAsV0FBVTtnREFBMENvQyxPQUFPUCxTQUFTUSxNQUFNOzBEQUM1RVIsU0FBU1EsTUFBTTs7Ozs7Ozs7Ozs7d0NBR25CNUUsYUFBYSxpQ0FDWiw4REFBQ3FFOzRDQUFHOUIsV0FBVTs7OERBQ1osOERBQUNPO29EQUFJUCxXQUFVOzhEQUNaNkIsU0FBU1MsYUFBYSxJQUFJOzs7Ozs7OERBRTdCLDhEQUFDL0I7b0RBQUlQLFdBQVU7OERBQ1o2QixTQUFTVSxjQUFjOzs7Ozs7Ozs7Ozs7c0RBSTlCLDhEQUFDVDs0Q0FBRzlCLFdBQVU7c0RBQ1haLFdBQVd5QyxTQUFTVyxTQUFTOzs7Ozs7c0RBRWhDLDhEQUFDVjs0Q0FBRzlCLFdBQVU7c0RBQ1g2QixTQUFTWSxVQUFVLGlCQUNsQiw4REFBQ2xDOztrRUFDQyw4REFBQ0E7a0VBQUtuQixXQUFXeUMsU0FBU1ksVUFBVTs7Ozs7O29EQUNuQ1osU0FBU2EsWUFBWSxrQkFDcEIsOERBQUNuQzt3REFBSVAsV0FBVTs7NERBQXdCOzREQUNqQzZCLFNBQVNhLFlBQVk7Ozs7Ozs7Ozs7Ozt1REFLL0I7Ozs7OztzREFHSiw4REFBQ1o7NENBQUc5QixXQUFVO3NEQUNaLDRFQUFDTztnREFBSVAsV0FBVTtnREFBMENvQyxPQUFPUCxTQUFTYyxRQUFROzBEQUM5RWQsU0FBU2MsUUFBUSxJQUFJOzs7Ozs7Ozs7Ozs7bUNBckRuQmQsU0FBU2UsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBZ0U3QjFFLGNBQWNBLFdBQVcyRSxVQUFVLEdBQUcsbUJBQ3JDLDhEQUFDdEM7Z0JBQUlQLFdBQVU7O2tDQUNiLDhEQUFDTzt3QkFBSVAsV0FBVTs7NEJBQXdCOzRCQUMxQjlCLENBQUFBLFdBQVdJLElBQUksR0FBRyxLQUFLSixXQUFXSyxLQUFLLEdBQUk7NEJBQUU7NEJBQUk7NEJBQzNEdUUsS0FBS0MsR0FBRyxDQUFDN0UsV0FBV0ksSUFBSSxHQUFHSixXQUFXSyxLQUFLLEVBQUVMLFdBQVc4RSxLQUFLOzRCQUFFOzRCQUFJOzRCQUNuRTlFLFdBQVc4RSxLQUFLOzRCQUFDOzs7Ozs7O2tDQUdwQiw4REFBQ3pDO3dCQUFJUCxXQUFVOzswQ0FDYiw4REFBQ2lEO2dDQUNDQyxTQUFTLElBQU1oRSxpQkFBaUJoQixXQUFXSSxJQUFJLEdBQUc7Z0NBQ2xENkUsVUFBVWpGLFdBQVdJLElBQUksSUFBSTtnQ0FDN0IwQixXQUFVOzBDQUNYOzs7Ozs7MENBSUQsOERBQUMrQjtnQ0FBSy9CLFdBQVU7O29DQUE4QztvQ0FDdEQ5QixXQUFXSSxJQUFJO29DQUFDO29DQUFLSixXQUFXMkUsVUFBVTs7Ozs7OzswQ0FHbEQsOERBQUNJO2dDQUNDQyxTQUFTLElBQU1oRSxpQkFBaUJoQixXQUFXSSxJQUFJLEdBQUc7Z0NBQ2xENkUsVUFBVWpGLFdBQVdJLElBQUksSUFBSUosV0FBVzJFLFVBQVU7Z0NBQ2xEN0MsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0F6VHdCeEM7O1FBSUxELDBEQUFPQTs7O0tBSkZDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2FwcHJvdmFscy9BcHByb3ZhbEhpc3RvcnkudHN4PzYwNjciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFxuICBnZXRBcHByb3ZhbHMsIFxuICBnZXRNeUFwcHJvdmFsUmVxdWVzdHMsXG4gIEFwcHJvdmFsUmVxdWVzdCwgXG4gIEFwcHJvdmFsRmlsdGVycyxcbiAgZ2V0U3RhdHVzQmFkZ2VDb2xvcixcbiAgZ2V0UmVxdWVzdFR5cGVEaXNwbGF5TmFtZSxcbiAgZm9ybWF0UmVxdWVzdERhdGFcbn0gZnJvbSAnQC9saWIvYXBwcm92YWxzJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0JztcblxuLyoqXG4gKiBQcm9wcyBmb3IgQXBwcm92YWxIaXN0b3J5IGNvbXBvbmVudFxuICovXG5pbnRlcmZhY2UgQXBwcm92YWxIaXN0b3J5UHJvcHMge1xuICB2aWV3TW9kZT86ICdteS1yZXF1ZXN0cycgfCAnYWxsLWFwcHJvdmFscyc7XG4gIHNob3dGaWx0ZXJzPzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiBBcHByb3ZhbEhpc3RvcnkgQ29tcG9uZW50XG4gKiBEaXNwbGF5cyBhcHByb3ZhbCByZXF1ZXN0IGhpc3Rvcnkgd2l0aCBzdGF0dXMgdHJhY2tpbmcgYW5kIGZpbHRlcmluZ1xuICovXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHByb3ZhbEhpc3RvcnkoeyBcbiAgdmlld01vZGUgPSAnbXktcmVxdWVzdHMnLFxuICBzaG93RmlsdGVycyA9IHRydWUgXG59OiBBcHByb3ZhbEhpc3RvcnlQcm9wcykge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKTtcbiAgY29uc3QgW2FwcHJvdmFscywgc2V0QXBwcm92YWxzXSA9IHVzZVN0YXRlPEFwcHJvdmFsUmVxdWVzdFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbcGFnaW5hdGlvbiwgc2V0UGFnaW5hdGlvbl0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBcbiAgLy8gRmlsdGVyc1xuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZTxBcHByb3ZhbEZpbHRlcnM+KHtcbiAgICBwYWdlOiAxLFxuICAgIGxpbWl0OiAyMFxuICB9KTtcblxuICAvKipcbiAgICogTG9hZCBhcHByb3ZhbHMgYmFzZWQgb24gdmlldyBtb2RlXG4gICAqL1xuICBjb25zdCBsb2FkQXBwcm92YWxzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICBcbiAgICAgIGxldCByZXN1bHQ7XG4gICAgICBpZiAodmlld01vZGUgPT09ICdteS1yZXF1ZXN0cycpIHtcbiAgICAgICAgcmVzdWx0ID0gYXdhaXQgZ2V0TXlBcHByb3ZhbFJlcXVlc3RzKGZpbHRlcnMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmVzdWx0ID0gYXdhaXQgZ2V0QXBwcm92YWxzKGZpbHRlcnMpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBzZXRBcHByb3ZhbHMocmVzdWx0LnJlcXVlc3RzKTtcbiAgICAgIHNldFBhZ2luYXRpb24ocmVzdWx0LnBhZ2luYXRpb24pO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCBhcHByb3ZhbCBoaXN0b3J5Jyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICogTG9hZCBhcHByb3ZhbHMgb24gY29tcG9uZW50IG1vdW50IGFuZCBmaWx0ZXIgY2hhbmdlc1xuICAgKi9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkQXBwcm92YWxzKCk7XG4gIH0sIFtmaWx0ZXJzLCB2aWV3TW9kZV0pO1xuXG4gIC8qKlxuICAgKiBIYW5kbGUgZmlsdGVyIGNoYW5nZXNcbiAgICovXG4gIGNvbnN0IGhhbmRsZUZpbHRlckNoYW5nZSA9IChrZXk6IGtleW9mIEFwcHJvdmFsRmlsdGVycywgdmFsdWU6IGFueSkgPT4ge1xuICAgIHNldEZpbHRlcnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtrZXldOiB2YWx1ZSxcbiAgICAgIHBhZ2U6IDEgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGZpbHRlcnMgY2hhbmdlXG4gICAgfSkpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBIYW5kbGUgcGFnaW5hdGlvblxuICAgKi9cbiAgY29uc3QgaGFuZGxlUGFnZUNoYW5nZSA9IChuZXdQYWdlOiBudW1iZXIpID0+IHtcbiAgICBzZXRGaWx0ZXJzKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBwYWdlOiBuZXdQYWdlXG4gICAgfSkpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBGb3JtYXQgZGF0ZSBmb3IgZGlzcGxheVxuICAgKi9cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgIH0pO1xuICB9O1xuXG4gIC8qKlxuICAgKiBHZXQgc3RhdHVzIGljb25cbiAgICovXG4gIGNvbnN0IGdldFN0YXR1c0ljb24gPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHN0YXR1cykge1xuICAgICAgY2FzZSAncGVuZGluZyc6XG4gICAgICAgIHJldHVybiAoXG4gICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQteWVsbG93LTUwMFwiIGZpbGw9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDIwIDIwXCI+XG4gICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTEwIDE4YTggOCAwIDEwMC0xNiA4IDggMCAwMDAgMTZ6bTEtMTJhMSAxIDAgMTAtMiAwdjRhMSAxIDAgMDAuMjkzLjcwN2wyLjgyOCAyLjgyOWExIDEgMCAxMDEuNDE1LTEuNDE1TDExIDkuNTg2VjZ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKTtcbiAgICAgIGNhc2UgJ2FwcHJvdmVkJzpcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi01MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2em0zLjcwNy05LjI5M2ExIDEgMCAwMC0xLjQxNC0xLjQxNEw5IDEwLjU4NiA3LjcwNyA5LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNGwyIDJhMSAxIDAgMDAxLjQxNCAwbDQtNHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApO1xuICAgICAgY2FzZSAncmVqZWN0ZWQnOlxuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXJlZC01MDBcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgPHBhdGggZmlsbFJ1bGU9XCJldmVub2RkXCIgZD1cIk0xMCAxOGE4IDggMCAxMDAtMTYgOCA4IDAgMDAwIDE2ek04LjcwNyA3LjI5M2ExIDEgMCAwMC0xLjQxNCAxLjQxNEw4LjU4NiAxMGwtMS4yOTMgMS4yOTNhMSAxIDAgMTAxLjQxNCAxLjQxNEwxMCAxMS40MTRsMS4yOTMgMS4yOTNhMSAxIDAgMDAxLjQxNC0xLjQxNEwxMS40MTQgMTBsMS4yOTMtMS4yOTNhMSAxIDAgMDAtMS40MTQtMS40MTRMMTAgOC41ODYgOC43MDcgNy4yOTN6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgKTtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfTtcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGgtNjRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNiB3LTYgYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDBcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbWRcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktNCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAge3ZpZXdNb2RlID09PSAnbXktcmVxdWVzdHMnID8gJ015IEFwcHJvdmFsIFJlcXVlc3RzJyA6ICdBbGwgQXBwcm92YWwgUmVxdWVzdHMnfVxuICAgICAgICA8L2gyPlxuXG4gICAgICAgIHsvKiBGaWx0ZXJzICovfVxuICAgICAgICB7c2hvd0ZpbHRlcnMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBmbGV4IGZsZXgtd3JhcCBnYXAtNFwiPlxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5zdGF0dXMgfHwgJyd9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmlsdGVyQ2hhbmdlKCdzdGF0dXMnLCBlLnRhcmdldC52YWx1ZSB8fCB1bmRlZmluZWQpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHRleHQtc21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+QWxsIFN0YXR1czwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwicGVuZGluZ1wiPlBlbmRpbmc8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImFwcHJvdmVkXCI+QXBwcm92ZWQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInJlamVjdGVkXCI+UmVqZWN0ZWQ8L29wdGlvbj5cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLnJlcXVlc3RUeXBlIHx8ICcnfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbHRlckNoYW5nZSgncmVxdWVzdFR5cGUnLCBlLnRhcmdldC52YWx1ZSB8fCB1bmRlZmluZWQpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHRleHQtc21cIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+QWxsIFR5cGVzPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb3JyZWN0aW9uXCI+VGltZSBDb3JyZWN0aW9uPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJvdmVydGltZVwiPk92ZXJ0aW1lPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsZWF2ZVwiPkxlYXZlPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlcnMuc3RhcnREYXRlIHx8ICcnfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbHRlckNoYW5nZSgnc3RhcnREYXRlJywgZS50YXJnZXQudmFsdWUgfHwgdW5kZWZpbmVkKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTdGFydCBEYXRlXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLmVuZERhdGUgfHwgJyd9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmlsdGVyQ2hhbmdlKCdlbmREYXRlJywgZS50YXJnZXQudmFsdWUgfHwgdW5kZWZpbmVkKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbmQgRGF0ZVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFcnJvciBNZXNzYWdlICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC02IG10LTQgcC0zIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogQXBwcm92YWxzIExpc3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICB7YXBwcm92YWxzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktOCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICBObyBhcHByb3ZhbCByZXF1ZXN0cyBmb3VuZC5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBTdGF0dXNcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIFR5cGVcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIFJlcXVlc3QgRGV0YWlsc1xuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgUmVhc29uXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICB7dmlld01vZGUgPT09ICdhbGwtYXBwcm92YWxzJyAmJiAoXG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIFJlcXVlc3RlclxuICAgICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIFN1Ym1pdHRlZFxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgUHJvY2Vzc2VkXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBDb21tZW50c1xuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImJnLXdoaXRlIGRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMFwiPlxuICAgICAgICAgICAgICB7YXBwcm92YWxzLm1hcCgoYXBwcm92YWwpID0+IChcbiAgICAgICAgICAgICAgICA8dHIga2V5PXthcHByb3ZhbC5pZH0gY2xhc3NOYW1lPVwiaG92ZXI6YmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFN0YXR1c0ljb24oYXBwcm92YWwuc3RhdHVzKX1cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BtbC0yIGlubGluZS1mbGV4IHB4LTIgcHktMSB0ZXh0LXhzIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1mdWxsICR7Z2V0U3RhdHVzQmFkZ2VDb2xvcihhcHByb3ZhbC5zdGF0dXMpfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLnN0YXR1cy5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIGFwcHJvdmFsLnN0YXR1cy5zbGljZSgxKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFJlcXVlc3RUeXBlRGlzcGxheU5hbWUoYXBwcm92YWwucmVxdWVzdFR5cGUpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZXF1ZXN0RGF0YShhcHByb3ZhbC5yZXF1ZXN0VHlwZSwgYXBwcm92YWwucmVxdWVzdERhdGEpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwIG1heC13LXhzIHRydW5jYXRlXCIgdGl0bGU9e2FwcHJvdmFsLnJlYXNvbn0+XG4gICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLnJlYXNvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAge3ZpZXdNb2RlID09PSAnYWxsLWFwcHJvdmFscycgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHthcHByb3ZhbC5yZXF1ZXN0ZXJOYW1lIHx8ICdVbmtub3duJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLnJlcXVlc3RlckVtYWlsfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREYXRlKGFwcHJvdmFsLmNyZWF0ZWRBdCl9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLmFwcHJvdmVkQXQgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+e2Zvcm1hdERhdGUoYXBwcm92YWwuYXBwcm92ZWRBdCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YXBwcm92YWwuYXBwcm92ZXJOYW1lICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBieSB7YXBwcm92YWwuYXBwcm92ZXJOYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgJy0nXG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMCBtYXgtdy14cyB0cnVuY2F0ZVwiIHRpdGxlPXthcHByb3ZhbC5jb21tZW50c30+XG4gICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLmNvbW1lbnRzIHx8ICctJ31cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQYWdpbmF0aW9uICovfVxuICAgICAge3BhZ2luYXRpb24gJiYgcGFnaW5hdGlvbi50b3RhbFBhZ2VzID4gMSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgU2hvd2luZyB7KChwYWdpbmF0aW9uLnBhZ2UgLSAxKSAqIHBhZ2luYXRpb24ubGltaXQpICsgMX0gdG97JyAnfVxuICAgICAgICAgICAge01hdGgubWluKHBhZ2luYXRpb24ucGFnZSAqIHBhZ2luYXRpb24ubGltaXQsIHBhZ2luYXRpb24udG90YWwpfSBvZnsnICd9XG4gICAgICAgICAgICB7cGFnaW5hdGlvbi50b3RhbH0gcmVzdWx0c1xuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlUGFnZUNoYW5nZShwYWdpbmF0aW9uLnBhZ2UgLSAxKX1cbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3BhZ2luYXRpb24ucGFnZSA8PSAxfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUHJldmlvdXNcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgIFBhZ2Uge3BhZ2luYXRpb24ucGFnZX0gb2Yge3BhZ2luYXRpb24udG90YWxQYWdlc31cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVQYWdlQ2hhbmdlKHBhZ2luYXRpb24ucGFnZSArIDEpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17cGFnaW5hdGlvbi5wYWdlID49IHBhZ2luYXRpb24udG90YWxQYWdlc31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgaG92ZXI6YmctZ3JheS01MCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIE5leHRcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImdldEFwcHJvdmFscyIsImdldE15QXBwcm92YWxSZXF1ZXN0cyIsImdldFN0YXR1c0JhZGdlQ29sb3IiLCJnZXRSZXF1ZXN0VHlwZURpc3BsYXlOYW1lIiwiZm9ybWF0UmVxdWVzdERhdGEiLCJ1c2VBdXRoIiwiQXBwcm92YWxIaXN0b3J5Iiwidmlld01vZGUiLCJzaG93RmlsdGVycyIsInVzZXIiLCJhcHByb3ZhbHMiLCJzZXRBcHByb3ZhbHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJwYWdpbmF0aW9uIiwic2V0UGFnaW5hdGlvbiIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwicGFnZSIsImxpbWl0IiwibG9hZEFwcHJvdmFscyIsInJlc3VsdCIsInJlcXVlc3RzIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwia2V5IiwidmFsdWUiLCJwcmV2IiwiaGFuZGxlUGFnZUNoYW5nZSIsIm5ld1BhZ2UiLCJmb3JtYXREYXRlIiwiZGF0ZVN0cmluZyIsIkRhdGUiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJ5ZWFyIiwibW9udGgiLCJkYXkiLCJob3VyIiwibWludXRlIiwiZ2V0U3RhdHVzSWNvbiIsInN0YXR1cyIsInN2ZyIsImNsYXNzTmFtZSIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwiZGl2IiwiaDIiLCJzZWxlY3QiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJ1bmRlZmluZWQiLCJvcHRpb24iLCJyZXF1ZXN0VHlwZSIsImlucHV0IiwidHlwZSIsInN0YXJ0RGF0ZSIsInBsYWNlaG9sZGVyIiwiZW5kRGF0ZSIsInAiLCJsZW5ndGgiLCJ0YWJsZSIsInRoZWFkIiwidHIiLCJ0aCIsInRib2R5IiwibWFwIiwiYXBwcm92YWwiLCJ0ZCIsInNwYW4iLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsInNsaWNlIiwicmVxdWVzdERhdGEiLCJ0aXRsZSIsInJlYXNvbiIsInJlcXVlc3Rlck5hbWUiLCJyZXF1ZXN0ZXJFbWFpbCIsImNyZWF0ZWRBdCIsImFwcHJvdmVkQXQiLCJhcHByb3Zlck5hbWUiLCJjb21tZW50cyIsImlkIiwidG90YWxQYWdlcyIsIk1hdGgiLCJtaW4iLCJ0b3RhbCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/approvals/ApprovalHistory.tsx\n"));

/***/ })

});