"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/users/page",{

/***/ "(app-pages-browser)/./src/components/users/UserProfile.tsx":
/*!**********************************************!*\
  !*** ./src/components/users/UserProfile.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserProfile; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_users__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/users */ \"(app-pages-browser)/./src/lib/users.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * UserProfile component for displaying detailed user information\n * Includes profile view, password change, and action buttons\n */ function UserProfile(param) {\n    let { user, onEdit, onClose, showActions = true } = param;\n    var _user_firstName, _user_lastName;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showPasswordForm, setShowPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordForm, setPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [passwordLoading, setPasswordLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [passwordError, setPasswordError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [passwordMessage, setPasswordMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    /**\n   * Check if current user can edit this profile\n   */ const canEdit = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"admin\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"manager\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) === user.id;\n    const canChangePassword = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) === user.id;\n    /**\n   * Handle password change\n   */ const handlePasswordChange = async (e)=>{\n        e.preventDefault();\n        if (passwordForm.newPassword !== passwordForm.confirmPassword) {\n            setPasswordError(\"Passwords do not match\");\n            return;\n        }\n        if (passwordForm.newPassword.length < 8) {\n            setPasswordError(\"Password must be at least 8 characters long\");\n            return;\n        }\n        setPasswordLoading(true);\n        setPasswordError(\"\");\n        setPasswordMessage(\"\");\n        try {\n            await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.updateUserPassword)(user.id, passwordForm.currentPassword, passwordForm.newPassword);\n            setPasswordMessage(\"Password updated successfully!\");\n            setPasswordForm({\n                currentPassword: \"\",\n                newPassword: \"\",\n                confirmPassword: \"\"\n            });\n            setShowPasswordForm(false);\n        } catch (err) {\n            setPasswordError(err.message || \"Failed to update password\");\n        } finally{\n            setPasswordLoading(false);\n        }\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    /**\n   * Format role for display\n   */ const formatRole = (role)=>{\n        return role ? role.charAt(0).toUpperCase() + role.slice(1) : \"\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 w-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl font-bold text-white\",\n                                        children: [\n                                            ((_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0)) || \"\",\n                                            ((_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName.charAt(0)) || \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: [\n                                                user.firstName,\n                                                \" \",\n                                                user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-indigo-100\",\n                                            children: user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.role === \"admin\" ? \"bg-purple-100 text-purple-800\" : user.role === \"manager\" ? \"bg-blue-100 text-blue-800\" : \"bg-gray-100 text-gray-800\"),\n                                                    children: formatRole(user.role)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(user.isActive ? \"bg-green-100 text-green-800\" : \"bg-red-100 text-red-800\"),\n                                                    children: user.isActive ? \"Active\" : \"Inactive\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-white hover:text-gray-200 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: \"2\",\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    passwordMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md\",\n                        children: passwordMessage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Personal Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Full Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: [\n                                                            user.firstName,\n                                                            \" \",\n                                                            user.lastName\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Email\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Work Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            user.employeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Employee ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.employeeId\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            user.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Department\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: user.department\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-500\",\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-900\",\n                                                        children: formatRole(user.role)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"Account Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Account Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium \".concat(user.isActive ? \"text-green-600\" : \"text-red-600\"),\n                                                children: user.isActive ? \"Active\" : \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Email Verified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium \".concat(user.emailVerified ? \"text-green-600\" : \"text-yellow-600\"),\n                                                children: user.emailVerified ? \"Verified\" : \"Not Verified\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Biometric Enabled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium \".concat(user.biometricEnabled ? \"text-green-600\" : \"text-gray-600\"),\n                                                children: user.biometricEnabled ? \"Enabled\" : \"Disabled\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Member Since\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    user.lastLogin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Last Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.lastLogin)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    user.updatedAt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-500\",\n                                                children: \"Last Updated\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-900\",\n                                                children: formatDate(user.updatedAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    canChangePassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Security\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowPasswordForm(!showPasswordForm),\n                                        className: \"text-indigo-600 hover:text-indigo-800 text-sm font-medium\",\n                                        children: showPasswordForm ? \"Cancel\" : \"Change Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            showPasswordForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handlePasswordChange,\n                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                children: [\n                                    passwordError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md text-sm\",\n                                        children: passwordError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"currentPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Current Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"currentPassword\",\n                                                        value: passwordForm.currentPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    currentPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"newPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"New Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"newPassword\",\n                                                        value: passwordForm.newPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    newPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"confirmPassword\",\n                                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Confirm Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        id: \"confirmPassword\",\n                                                        value: passwordForm.confirmPassword,\n                                                        onChange: (e)=>setPasswordForm((prev)=>({\n                                                                    ...prev,\n                                                                    confirmPassword: e.target.value\n                                                                })),\n                                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                                                        disabled: passwordLoading,\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: passwordLoading,\n                                            className: \"bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                            children: passwordLoading ? \"Updating...\" : \"Update Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this),\n                    showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200\",\n                        children: [\n                            canEdit && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onEdit,\n                                className: \"bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-md font-medium\",\n                                children: \"Edit Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this),\n                            onClose && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md font-medium\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserProfile.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(UserProfile, \"g7q0qrAv8EFBn09niSrWAyBgP9g=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/UserProfile.tsx\n"));

/***/ })

});