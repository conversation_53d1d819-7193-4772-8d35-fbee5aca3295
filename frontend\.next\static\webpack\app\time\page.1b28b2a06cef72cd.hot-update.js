"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/time/page",{

/***/ "(app-pages-browser)/./src/components/time/TimeLogs.tsx":
/*!******************************************!*\
  !*** ./src/components/time/TimeLogs.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TimeLogs; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var _components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/approvals/ApprovalRequestForm */ \"(app-pages-browser)/./src/components/approvals/ApprovalRequestForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\r\n * Time Logs component for viewing and managing time entries\r\n * Displays paginated list of time logs with filtering options\r\n */ function TimeLogs() {\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [timeLogs, setTimeLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Filter states\n    const [startDate, setStartDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [endDate, setEndDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Approval request modal state\n    const [showApprovalModal, setShowApprovalModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTimeLog, setSelectedTimeLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Initialize with current week\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const now = new Date();\n        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));\n        const weekEnd = new Date(weekStart);\n        weekEnd.setDate(weekStart.getDate() + 6);\n        setStartDate(weekStart.toISOString().split(\"T\")[0]);\n        setEndDate(weekEnd.toISOString().split(\"T\")[0]);\n    }, []);\n    // Load time logs when filters or page changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (startDate && endDate) {\n            loadTimeLogs();\n        }\n    }, [\n        currentPage,\n        startDate,\n        endDate,\n        status\n    ]);\n    /**\r\n   * Load time logs from API with current filters\r\n   */ const loadTimeLogs = async ()=>{\n        setIsLoading(true);\n        setError(\"\");\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: \"10\",\n                start_date: startDate,\n                end_date: endDate,\n                ...status !== \"all\" && {\n                    status\n                }\n            });\n            const response = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.apiRequest)(\"/time/logs?\".concat(params));\n            if (response.success && response.data) {\n                const data = response.data;\n                setTimeLogs(data.logs);\n                setTotalPages(data.totalPages);\n                setTotalCount(data.totalCount);\n            } else {\n                setError(response.message || \"Failed to load time logs\");\n            }\n        } catch (error) {\n            setError(\"Error loading time logs. Please try again.\");\n            console.error(\"Time logs error:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    /**\r\n   * Handle filter changes\r\n   */ const handleFilterChange = ()=>{\n        setCurrentPage(1); // Reset to first page when filters change\n        loadTimeLogs();\n    };\n    /**\r\n   * Handle page changes\r\n   */ const handlePageChange = (page)=>{\n        setCurrentPage(page);\n    };\n    /**\r\n   * Format date for display\r\n   */ const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"en-US\", {\n            month: \"short\",\n            day: \"numeric\",\n            year: \"numeric\"\n        });\n    };\n    /**\r\n   * Format time for display\r\n   */ const formatTime = (dateString)=>{\n        const date = new Date(dateString);\n        return date.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\",\n            hour12: true\n        });\n    };\n    /**\r\n   * Get status badge color\r\n   */ const getStatusBadge = (status)=>{\n        const colors = {\n            active: \"bg-blue-100 text-blue-800\",\n            completed: \"bg-green-100 text-green-800\",\n            approved: \"bg-purple-100 text-purple-800\"\n        };\n        return colors[status] || \"bg-gray-100 text-gray-800\";\n    };\n    /**\r\n   * Calculate total hours for the period\r\n   */ const getTotalHours = ()=>{\n        return timeLogs.reduce((total, log)=>{\n            if (log.total_hours) {\n                const hours = parseFloat(log.total_hours);\n                return total + hours;\n            }\n            return total;\n        }, 0).toFixed(2);\n    };\n    /**\r\n   * Set quick date filters\r\n   */ const setQuickDateFilter = (days)=>{\n        const end = new Date();\n        const start = new Date();\n        start.setDate(end.getDate() - days);\n        setStartDate(start.toISOString().split(\"T\")[0]);\n        setEndDate(end.toISOString().split(\"T\")[0]);\n    };\n    /**\r\n   * Filter time logs based on search term\r\n   */ const filteredTimeLogs = timeLogs.filter((log)=>{\n        var _log_clock_in_location, _log_notes;\n        if (!searchTerm) return true;\n        const searchLower = searchTerm.toLowerCase();\n        return ((_log_clock_in_location = log.clock_in_location) === null || _log_clock_in_location === void 0 ? void 0 : _log_clock_in_location.toLowerCase().includes(searchLower)) || ((_log_notes = log.notes) === null || _log_notes === void 0 ? void 0 : _log_notes.toLowerCase().includes(searchLower)) || log.status.toLowerCase().includes(searchLower);\n    });\n    /**\r\n   * Open approval request modal\r\n   */ const openApprovalModal = (timeLog)=>{\n        setSelectedTimeLog(timeLog);\n        setShowApprovalModal(true);\n    };\n    /**\r\n   * Close approval request modal\r\n   */ const closeApprovalModal = ()=>{\n        setShowApprovalModal(false);\n        setSelectedTimeLog(null);\n    };\n    /**\r\n   * Handle approval request success\r\n   */ const handleApprovalSuccess = ()=>{\n        closeApprovalModal();\n        // Optionally refresh the time logs to show updated status\n        loadTimeLogs();\n    };\n    /**\r\n   * Check if time log can be corrected\r\n   */ const canRequestCorrection = (timeLog)=>{\n        // Only allow correction requests for completed time logs\n        return timeLog.status === \"completed\" && timeLog.clock_out_time;\n    };\n    /**\r\n   * Export time logs to CSV\r\n   */ const exportToCSV = ()=>{\n        const headers = [\n            \"Date\",\n            \"Clock In\",\n            \"Clock Out\",\n            \"Total Hours\",\n            \"Location\",\n            \"Status\",\n            \"Notes\"\n        ];\n        const csvData = filteredTimeLogs.map((log)=>[\n                formatDate(log.clock_in_time),\n                formatTime(log.clock_in_time),\n                log.clock_out_time ? formatTime(log.clock_out_time) : \"\",\n                log.total_hours || \"\",\n                log.clock_in_location || \"\",\n                log.status,\n                log.notes || \"\"\n            ]);\n        const csvContent = [\n            headers,\n            ...csvData\n        ].map((row)=>row.map((field)=>'\"'.concat(field, '\"')).join(\",\")).join(\"\\n\");\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: \"text/csv\"\n        });\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"time-logs-\".concat(startDate, \"-to-\").concat(endDate, \".csv\");\n        a.click();\n        window.URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800 mb-1\",\n                                    children: \"Time Logs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"View and manage your time entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 sm:mt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportToCSV,\n                                disabled: filteredTimeLogs.length === 0,\n                                className: \"btn btn-success\",\n                                children: \"\\uD83D\\uDCE5 Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-50 rounded-lg p-3 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-2 lg:mb-0\",\n                                children: \"Search & Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(0),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Today\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(7),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 7 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(30),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 30 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setQuickDateFilter(90),\n                                        className: \"btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800\",\n                                        children: \"Last 3 Months\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                    children: \"\\uD83D\\uDD0D\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search by location, notes, or status...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"form-input pl-10\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"startDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Start Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"startDate\",\n                                        value: startDate,\n                                        onChange: (e)=>setStartDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"endDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"End Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"endDate\",\n                                        value: endDate,\n                                        onChange: (e)=>setEndDate(e.target.value),\n                                        className: \"form-input\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"status\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"status\",\n                                        value: status,\n                                        onChange: (e)=>setStatus(e.target.value),\n                                        className: \"form-input\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"all\",\n                                                children: \"All Statuses\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"active\",\n                                                children: \"Active\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"completed\",\n                                                children: \"Completed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"approved\",\n                                                children: \"Approved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleFilterChange,\n                                    disabled: isLoading,\n                                    className: \"btn btn-primary w-full\",\n                                    children: isLoading ? \"Loading...\" : \"Apply Filters\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            timeLogs.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Entries\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: totalCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Showing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: filteredTimeLogs.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Total Hours\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl font-bold text-blue-800\",\n                                    children: getTotalHours()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-600\",\n                                    children: \"Date Range\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"font-semibold text-blue-800\",\n                                    children: [\n                                        formatDate(startDate),\n                                        \" - \",\n                                        formatDate(endDate)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 406,\n                columnNumber: 9\n            }, this),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"ml-2 text-gray-600\",\n                        children: \"Loading time logs...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this) : filteredTimeLogs.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: timeLogs.length === 0 ? \"No time logs found for the selected period.\" : \"No time logs match your search criteria.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 418,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Clock In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Clock Out\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Total Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Location\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: filteredTimeLogs.map((log)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                            children: formatDate(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                            children: formatTime(log.clock_in_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n                                            children: log.clock_out_time ? formatTime(log.clock_out_time) : \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\",\n                                            children: log.total_hours || \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: log.clock_in_location || \"Not specified\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(getStatusBadge(log.status)),\n                                                children: log.status.charAt(0).toUpperCase() + log.status.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                                            children: canRequestCorrection(log) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>openApprovalModal(log),\n                                                className: \"text-blue-600 hover:text-blue-900 transition duration-200\",\n                                                children: \"Request Correction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-400\",\n                                                children: \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, log.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 426,\n                columnNumber: 9\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing page \",\n                            currentPage,\n                            \" of \",\n                            totalPages,\n                            \" (\",\n                            totalCount,\n                            \" total entries)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage - 1),\n                                disabled: currentPage === 1,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Previous\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 13\n                            }, this),\n                            Array.from({\n                                length: Math.min(5, totalPages)\n                            }, (_, i)=>{\n                                const page = i + 1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handlePageChange(page),\n                                    className: \"px-3 py-1 text-sm rounded-md transition duration-200 \".concat(currentPage === page ? \"bg-blue-600 text-white\" : \"bg-gray-200 hover:bg-gray-300\"),\n                                    children: page\n                                }, page, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this);\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handlePageChange(currentPage + 1),\n                                disabled: currentPage === totalPages,\n                                className: \"px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200\",\n                                children: \"Next\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                                lineNumber: 525,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 501,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 497,\n                columnNumber: 9\n            }, this),\n            showApprovalModal && selectedTimeLog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_approvals_ApprovalRequestForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        timeLogId: selectedTimeLog.id,\n                        currentClockIn: selectedTimeLog.clock_in_time,\n                        currentClockOut: selectedTimeLog.clock_out_time || \"\",\n                        onSuccess: handleApprovalSuccess,\n                        onCancel: closeApprovalModal\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                        lineNumber: 540,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n                lineNumber: 538,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\time\\\\TimeLogs.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, this);\n}\n_s(TimeLogs, \"h+UOzgzavemObQz7KGM+r5yYl2A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = TimeLogs;\nvar _c;\n$RefreshReg$(_c, \"TimeLogs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/time/TimeLogs.tsx\n"));

/***/ })

});