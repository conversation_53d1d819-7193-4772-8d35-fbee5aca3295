# Global Rules
!IMPORTANT! ALL code files should be under 500 lines.
!IMPORTANT! Always use Context7 MCP Server.
## 1. Response Style
- `concise` — Limit to essential content without filler
- No preambles or summarizing the prompt
- Avoid modal hedging
**Expand when**:
- Response involves `high_risk_changes`, `unexpected_results`, or `multiple_options`
- Additional explanation materially impacts decision-making
- Brevity would compromise safety or clarity
## 2. Code Standards
### 2.1 Organization
- src/modules/     - Feature-level domain modules
- src/components/  - Reusable UI components (1 per folder)
- src/shared/      - Shared helpers and utilities
- src/models/      - Domain models, DTOs, and types
- tests/           - Mirrors src/; use <unit>.spec.ts format
- scripts/         - One-off CLI or migration scripts
> **Rule:** All files must follow this layout. Block out-of-place commits
### 2.2 Quality
- Add doc-blocks to all exported functions/classes
- Limit functions to single responsibility
- Limit nesting to 3 levels
- Use descriptive, non-abbreviated names
- Apply DRY principle (eliminate duplication)
- Enforce import order: standard → external → shared → local
**Naming Conventions**:
- Prefix interfaces with `I` (e.g., `IUserSession`)
- Use `SCREAMING_SNAKE_CASE` for constants
- Prefix private fields with `_`
### 2.3 Documentation Requirements
```ts
/**
 * Summary – high-level purpose of the function.
 *
 * @param userId   UUID of the user performing the action
 * @returns        Persisted record ID
 * @throws         ValidationError | DbConflictError
 * @example
 *   await createInvoice(userId, draft);
 */
```
- Document **why**, not just **what**
- Reference related code with `@see src/modules/...`
## 3. Error Handling & Logging
> **Rule:** Always diagnose the root cause and propose a specific fix
| Element | Values |
|-|-|
| **Triggers** | `error`, `exception`, `failed`, `crash`, `bug` |
| **Required Info** | `code_changes`, `stack_trace`, `config` |
### 3.1 Error Handling
- Use typed errors with clear messages
- Chain errors with `cause`: `new AppError(..., { cause })`
- Handle all error branches explicitly
- Never expose secrets or PII
### 3.2 Logging Standards
| Level | Use Case | Required Fields |
|-|-|-|
| `ERROR` | Failures | `requestId`, `stack`, `actor` |
| `WARN` | Recoverable anomalies | `requestId`, `reason` |
| `INFO` | Business events | `requestId`, domain data |
| `DEBUG` | Developer diagnostics | `requestId`, snapshot |
- Emit logs as structured JSON
- Redact all PII before logging
## 4. Documentation Standards
> **Rule:** Documentation is part of "Done". Every PR must include relevant documentation updates
### 4.1 Folder Layout (`.project/docs/`)
| Path | Contents | Naming Convention |
|-|-|-|
| `current_task.md` | Active TODO list | Checklist format |
| `tech_stack.md` | Architecture decisions (ADRs) | One section per decision |
| `changelog.md` | Human-readable version log | Date-descending order |
| `architecture/` | System diagrams by type | `.mmd` = Mermaid source files |
| ├─ `high_level/` | Context and container diagrams | `ctx-*.mmd` |
| ├─ `low_level/` | Component and sequence diagrams | `cmp-*.mmd` |
| ├─ `workflows/` | User journeys and flows | `flow-*.mmd` |
| ├─ `modules/` | Module-specific diagrams | `mod-<n>.mmd` |
| └─ `db_schemas/` | ERDs, joins, mappings | `db-*.mmd` |
> **Rule:** Always commit both `.mmd` and generated `.svg` together
### 4.2 Style Guide
- Use bullet lists over longform prose
- Add status badges to files:
  `![status](https://img.shields.io/badge/docs-up_to_date-brightgreen)`
- Use tables for structured data
- Add YAML frontmatter with title, status, owner, and updated date
### 4.3 Maintenance Rules
- Run `npm run docs:lint` weekly
- Archive outdated docs to `.project/docs/archive/yyyy-mm/`
## 5. Build Management
| Element | Items |
|-|-|
| **Require Approval** | `server_restarts`, `dependency_changes`, `db_migrations` |
| **Verify** | `paths_relative`, `commands_safe` |
- Tag dev-only additions with `@safe-devdep` in commit/PR
- Shell scripts must verify environment before execution
### 5.1 Package Governance
- Only install packages with:
  - ≥ 100 weekly downloads
  - Recent maintenance (within 6 months)
- Prefer built-ins or shared utilities over new packages
- Document new packages in `tech_stack.md`
- Run dependency audits in CI
## 6. Memory & Context Management
> **Rule:** Preserve high-signal facts and configurations
### 6.1 Memory Anchoring
- **Triggers**: Module/service changes, permanent decisions, new architecture diagrams
- **Do Not Memorize**: Transient paths, experimental configs, tooling output
- **Pattern Example**: "Uses Azure Cognitive Search for vectors. Integrates with OneDrive via Graph API"
- **Commit Rhythm**: Update memory every 3-4 steps; capture what was done and why
### 6.2 Context Scoping Metadata
> **Rule:** Enable scoped memory with embedded metadata at file top
```html
<!-- winds_memory:module=data-ingest -->
<!-- winds_memory:domain=shared -->
<!-- winds_memory:priority=high -->
```
### 6.3 Memory Validation
- Flag memories older than 30 days with no active reference
- Remove entries for deleted files or modules
- Run `npm run winds:mem-validate` weekly
- Schedule validation cleanup for self-healing systems
## 7. Workflow Orchestration
> **Rule:** Coordinate complex workflows through structured task decomposition and delegation
### 7.1 Task Management
- Break complex tasks into logical subtasks
- Track progress via clear status updates
- Synthesize results when subtasks complete
- Request clarification for unclear requirements
## 8. Hallucination Prevention
> **Rule:** Default to `defer_and_verify` mode for missing/ambiguous context
- Never guess APIs, functions, or parameters
- Prefer "Unknown" over unverifiable responses
- Reference canonical sources when available
