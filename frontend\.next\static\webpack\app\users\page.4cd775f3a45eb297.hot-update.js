"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/users/page",{

/***/ "(app-pages-browser)/./src/components/users/UserList.tsx":
/*!*******************************************!*\
  !*** ./src/components/users/UserList.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ UserList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_users__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/users */ \"(app-pages-browser)/./src/lib/users.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * UserList component for displaying and managing users\n * Includes pagination, search, filtering, and role-based actions\n */ function UserList(param) {\n    let { onUserSelect, onUserEdit, onUserCreate } = param;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Pagination and filtering state\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // UI state\n    const [selectedUsers, setSelectedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const itemsPerPage = 20;\n    // Load users on component mount and when filters/page change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUsers();\n    }, [\n        currentPage,\n        filters\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setTimeout(()=>{\n            if (searchQuery !== (filters.search || \"\")) {\n                setFilters((prev)=>({\n                        ...prev,\n                        search: searchQuery\n                    }));\n                setCurrentPage(1);\n            }\n        }, 500);\n        return ()=>clearTimeout(timer);\n    }, [\n        searchQuery\n    ]);\n    /**\n   * Load users from API\n   */ const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            setError(\"\");\n            const response = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.getAllUsers)(filters, currentPage, itemsPerPage);\n            console.log(\"Loaded users:\", response.users.map((u)=>({\n                    id: u.id,\n                    email: u.email,\n                    isActive: u.isActive,\n                    type: typeof u.isActive\n                })));\n            setUsers(response.users);\n            setTotalPages(response.pagination.totalPages);\n            setTotalUsers(response.pagination.total);\n        } catch (err) {\n            setError(\"Failed to load users. Please try again.\");\n            console.error(\"Error loading users:\", err);\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Handle user deletion\n   */ const handleDeleteUser = async (userId)=>{\n        if (!window.confirm(\"Are you sure you want to deactivate this user?\")) {\n            return;\n        }\n        try {\n            await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.deleteUser)(userId);\n            setMessage(\"User deactivated successfully\");\n            await loadUsers();\n        } catch (err) {\n            setError(\"Failed to deactivate user\");\n            console.error(\"Error deleting user:\", err);\n        }\n    };\n    /**\n   * Handle user status toggle\n   */ const handleToggleStatus = async (userId, isActive)=>{\n        try {\n            console.log(\"Toggling user \".concat(userId, \" from \").concat(isActive, \" to \").concat(!isActive));\n            const updatedUser = await (0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.toggleUserStatus)(userId, !isActive);\n            console.log(\"Updated user received:\", updatedUser);\n            setMessage(\"User \".concat(!isActive ? \"activated\" : \"deactivated\", \" successfully\"));\n            await loadUsers();\n        } catch (err) {\n            setError(\"Failed to update user status\");\n            console.error(\"Error toggling user status:\", err);\n        }\n    };\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value === \"\" ? undefined : value\n            }));\n        setCurrentPage(1);\n    };\n    /**\n   * Clear all filters\n   */ const clearFilters = ()=>{\n        setFilters({});\n        setSearchQuery(\"\");\n        setCurrentPage(1);\n    };\n    /**\n   * Handle bulk actions\n   */ const handleBulkAction = async (action)=>{\n        if (selectedUsers.size === 0) {\n            setError(\"Please select users first\");\n            return;\n        }\n        if (!window.confirm(\"Are you sure you want to \".concat(action, \" \").concat(selectedUsers.size, \" user(s)?\"))) {\n            return;\n        }\n        try {\n            const promises = Array.from(selectedUsers).map((userId)=>(0,_lib_users__WEBPACK_IMPORTED_MODULE_3__.toggleUserStatus)(userId, action === \"activate\"));\n            await Promise.all(promises);\n            setMessage(\"\".concat(selectedUsers.size, \" user(s) \").concat(action, \"d successfully\"));\n            setSelectedUsers(new Set());\n            await loadUsers();\n        } catch (err) {\n            setError(\"Failed to \".concat(action, \" users\"));\n            console.error(\"Error in bulk \".concat(action, \":\"), err);\n        }\n    };\n    /**\n   * Check if current user can perform admin actions\n   */ const canPerformAdminActions = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"admin\";\n    const canPerformManagerActions = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"admin\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"manager\";\n    /**\n   * Format user role for display\n   */ const formatRole = (role)=>{\n        return role ? role.charAt(0).toUpperCase() + role.slice(1) : \"\";\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Users\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        totalUsers,\n                                        \" total users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 11\n                        }, this),\n                        canPerformAdminActions && onUserCreate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onUserCreate,\n                            className: \"btn btn-primary\",\n                            children: \"Add User\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-3 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search users by name or email...\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value),\n                                    className: \"form-input\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowFilters(!showFilters),\n                                className: \"btn btn-outline\",\n                                children: [\n                                    \"Filters \",\n                                    showFilters ? \"▲\" : \"▼\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 grid grid-cols-1 sm:grid-cols-3 gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.role || \"\",\n                                onChange: (e)=>handleFilterChange(\"role\", e.target.value),\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"admin\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"manager\",\n                                        children: \"Manager\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"employee\",\n                                        children: \"Employee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.isActive === undefined ? \"\" : filters.isActive.toString(),\n                                onChange: (e)=>handleFilterChange(\"isActive\", e.target.value),\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"true\",\n                                        children: \"Active\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"false\",\n                                        children: \"Inactive\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearFilters,\n                                className: \"btn btn-outline\",\n                                children: \"Clear Filters\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            canPerformAdminActions && selectedUsers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-gray-50 border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                selectedUsers.size,\n                                \" user(s) selected\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleBulkAction(\"activate\"),\n                            className: \"btn btn-sm btn-success\",\n                            children: \"Activate\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>handleBulkAction(\"deactivate\"),\n                            className: \"btn btn-sm btn-error\",\n                            children: \"Deactivate\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 273,\n                columnNumber: 9\n            }, this),\n            (message || error) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-3 border-b border-gray-200\",\n                children: [\n                    message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-green-600 text-sm\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-red-600 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"Loading users...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 11\n                }, this) : users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No users found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 314,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"table-header\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedUsers.size === users.length && users.length > 0,\n                                            onChange: (e)=>{\n                                                if (e.target.checked) {\n                                                    setSelectedUsers(new Set(users.map((u)=>u.id)));\n                                                } else {\n                                                    setSelectedUsers(new Set());\n                                                }\n                                            },\n                                            className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"User\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Role\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Last Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"table-header-cell\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: users.map((user)=>{\n                                var _user_firstName, _user_lastName;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"table-row\",\n                                    children: [\n                                        canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedUsers.has(user.id),\n                                                onChange: (e)=>{\n                                                    const newSelected = new Set(selectedUsers);\n                                                    if (e.target.checked) {\n                                                        newSelected.add(user.id);\n                                                    } else {\n                                                        newSelected.delete(user.id);\n                                                    }\n                                                    setSelectedUsers(newSelected);\n                                                },\n                                                className: \"rounded border-gray-300 text-indigo-600 focus:ring-indigo-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 icon-container\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"icon-container bg-indigo-100\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-indigo-600\",\n                                                                children: [\n                                                                    ((_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0)) || \"\",\n                                                                    ((_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName.charAt(0)) || \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                children: [\n                                                                    user.firstName,\n                                                                    \" \",\n                                                                    user.lastName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: user.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            user.department && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: user.department\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"badge \".concat(user.role === \"admin\" ? \"badge-primary\" : user.role === \"manager\" ? \"badge-secondary\" : \"bg-gray-100 text-gray-800\"),\n                                                children: formatRole(user.role)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"badge \".concat(user.isActive ? \"badge-success\" : \"badge-error\"),\n                                                children: user.isActive ? \"Active\" : \"Inactive\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell text-gray-500\",\n                                            children: user.lastLogin ? formatDate(user.lastLogin) : \"Never\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"table-cell font-medium\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    onUserSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onUserSelect(user),\n                                                        className: \"text-indigo-600 hover:text-indigo-900\",\n                                                        children: \"View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    canPerformManagerActions && onUserEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>onUserEdit(user),\n                                                        className: \"text-blue-600 hover:text-blue-900\",\n                                                        children: \"Edit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    canPerformAdminActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleToggleStatus(user.id, user.isActive),\n                                                            className: \"\".concat(user.isActive ? \"text-red-600 hover:text-red-900\" : \"text-green-600 hover:text-green-900\"),\n                                                            children: user.isActive ? \"Deactivate\" : \"Activate\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, user.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * itemsPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * itemsPerPage, totalUsers),\n                                \" of \",\n                                totalUsers,\n                                \" users\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 text-sm text-gray-700\",\n                                    children: [\n                                        \"Page \",\n                                        currentPage,\n                                        \" of \",\n                                        totalPages\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-1 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                                    lineNumber: 485,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n                lineNumber: 467,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\users\\\\UserList.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(UserList, \"+5g9CCtoXFoB4g34oCREXMYLkB0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/UserList.tsx\n"));

/***/ })

});