"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/approvals/page",{

/***/ "(app-pages-browser)/./src/components/approvals/ManagerApprovalInterface.tsx":
/*!***************************************************************!*\
  !*** ./src/components/approvals/ManagerApprovalInterface.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ManagerApprovalInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(app-pages-browser)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n/**\n * ManagerApprovalInterface Component\n * Interface for managers to view, approve, and reject pending approval requests\n */ function ManagerApprovalInterface(param) {\n    let { onApprovalProcessed } = param;\n    _s();\n    const [approvals, setApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedApprovals, setSelectedApprovals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [processModal, setProcessModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filters\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        status: \"pending\"\n    });\n    /**\n   * Load pending approvals\n   */ const loadApprovals = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const result = await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getPendingApprovals)(filters);\n            setApprovals(result.requests);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load approvals\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    /**\n   * Load approvals on component mount and filter changes\n   */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadApprovals();\n    }, [\n        filters\n    ]);\n    /**\n   * Handle filter changes\n   */ const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1 // Reset to first page when filters change\n            }));\n    };\n    /**\n   * Handle individual approval selection\n   */ const handleApprovalSelect = (approvalId)=>{\n        setSelectedApprovals((prev)=>{\n            const newSet = new Set(prev);\n            if (newSet.has(approvalId)) {\n                newSet.delete(approvalId);\n            } else {\n                newSet.add(approvalId);\n            }\n            return newSet;\n        });\n    };\n    /**\n   * Handle select all approvals\n   */ const handleSelectAll = ()=>{\n        if (selectedApprovals.size === approvals.length) {\n            setSelectedApprovals(new Set());\n        } else {\n            setSelectedApprovals(new Set(approvals.map((a)=>a.id)));\n        }\n    };\n    /**\n   * Open process modal\n   */ const openProcessModal = (approval, action)=>{\n        setProcessModal({\n            approval,\n            action\n        });\n        setComments(\"\");\n    };\n    /**\n   * Close process modal\n   */ const closeProcessModal = ()=>{\n        setProcessModal(null);\n        setComments(\"\");\n    };\n    /**\n   * Process single approval\n   */ const handleProcessApproval = async ()=>{\n        if (!processModal) return;\n        setProcessing(true);\n        try {\n            await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.processApproval)(processModal.approval.id, {\n                status: processModal.action === \"approve\" ? \"approved\" : \"rejected\",\n                comments: comments.trim() || undefined\n            });\n            // Refresh the list\n            await loadApprovals();\n            // Clear selection\n            setSelectedApprovals(new Set());\n            // Close modal\n            closeProcessModal();\n            if (onApprovalProcessed) {\n                onApprovalProcessed();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to process approval\");\n        } finally{\n            setProcessing(false);\n        }\n    };\n    /**\n   * Process bulk approvals\n   */ const handleBulkProcess = async (action)=>{\n        if (selectedApprovals.size === 0) return;\n        setProcessing(true);\n        try {\n            const promises = Array.from(selectedApprovals).map((id)=>(0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.processApproval)(id, {\n                    status: action === \"approve\" ? \"approved\" : \"rejected\",\n                    comments: \"Bulk \".concat(action, \"d by manager\")\n                }));\n            await Promise.all(promises);\n            // Refresh the list\n            await loadApprovals();\n            // Clear selection\n            setSelectedApprovals(new Set());\n            if (onApprovalProcessed) {\n                onApprovalProcessed();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to process bulk approvals\");\n        } finally{\n            setProcessing(false);\n        }\n    };\n    /**\n   * Format date for display\n   */ const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"short\",\n            day: \"numeric\",\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 207,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n            lineNumber: 206,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-4 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900\",\n                                children: [\n                                    \"Pending Approvals (\",\n                                    approvals.length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            selectedApprovals.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleBulkProcess(\"approve\"),\n                                        disabled: processing,\n                                        className: \"px-3 py-1 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:opacity-50\",\n                                        children: [\n                                            \"Approve (\",\n                                            selectedApprovals.size,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleBulkProcess(\"reject\"),\n                                        disabled: processing,\n                                        className: \"px-3 py-1 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50\",\n                                        children: [\n                                            \"Reject (\",\n                                            selectedApprovals.size,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex flex-wrap gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: filters.requestType || \"\",\n                                onChange: (e)=>handleFilterChange(\"requestType\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.startDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"startDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"Start Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                value: filters.endDate || \"\",\n                                onChange: (e)=>handleFilterChange(\"endDate\", e.target.value || undefined),\n                                className: \"px-3 py-2 border border-gray-300 rounded-md text-sm\",\n                                placeholder: \"End Date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 275,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: approvals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-6 py-8 text-center text-gray-500\",\n                    children: \"No pending approvals found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"bg-gray-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: selectedApprovals.size === approvals.length && approvals.length > 0,\n                                            onChange: handleSelectAll,\n                                            className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Employee\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Request Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Reason\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Submitted\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white divide-y divide-gray-200\",\n                            children: approvals.map((approval)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: selectedApprovals.has(approval.id),\n                                                onChange: ()=>handleApprovalSelect(approval.id),\n                                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: approval.requesterName || \"Unknown\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: approval.requesterEmail\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getStatusBadgeColor)(\"pending\")),\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(approval.requestType)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900\",\n                                                children: (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.formatRequestData)(approval.requestType, approval.requestData)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-900 max-w-xs truncate\",\n                                                children: approval.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                                            children: formatDate(approval.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openProcessModal(approval, \"approve\"),\n                                                    className: \"text-green-600 hover:text-green-900\",\n                                                    children: \"Approve\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openProcessModal(approval, \"reject\"),\n                                                    className: \"text-red-600 hover:text-red-900\",\n                                                    children: \"Reject\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, approval.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            processModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: [\n                                    processModal.action === \"approve\" ? \"Approve\" : \"Reject\",\n                                    \" Request\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 p-3 bg-gray-50 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Employee:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            processModal.approval.requesterName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Type:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.getRequestTypeDisplayName)(processModal.approval.requestType)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Reason:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            processModal.approval.reason\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Comments (Optional)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: comments,\n                                        onChange: (e)=>setComments(e.target.value),\n                                        rows: 3,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        placeholder: \"Add any comments...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: closeProcessModal,\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleProcessApproval,\n                                        disabled: processing,\n                                        className: \"px-4 py-2 text-sm font-medium text-white rounded-md disabled:opacity-50 \".concat(processModal.action === \"approve\" ? \"bg-green-600 hover:bg-green-700\" : \"bg-red-600 hover:bg-red-700\"),\n                                        children: processing ? \"Processing...\" : processModal.action === \"approve\" ? \"Approve\" : \"Reject\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                        lineNumber: 417,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ManagerApprovalInterface.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n_s(ManagerApprovalInterface, \"4au/pXT5K5Gea9CtvadSktTQMjY=\");\n_c = ManagerApprovalInterface;\nvar _c;\n$RefreshReg$(_c, \"ManagerApprovalInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2FwcHJvdmFscy9NYW5hZ2VyQXBwcm92YWxJbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFbUQ7QUFTMUI7QUFpQnpCOzs7Q0FHQyxHQUNjLFNBQVNRLHlCQUF5QixLQUFzRDtRQUF0RCxFQUFFQyxtQkFBbUIsRUFBaUMsR0FBdEQ7O0lBQy9DLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHViwrQ0FBUUEsQ0FBb0IsRUFBRTtJQUNoRSxNQUFNLENBQUNXLFNBQVNDLFdBQVcsR0FBR1osK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDYSxPQUFPQyxTQUFTLEdBQUdkLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNlLG1CQUFtQkMscUJBQXFCLEdBQUdoQiwrQ0FBUUEsQ0FBYyxJQUFJaUI7SUFDNUUsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBR25CLCtDQUFRQSxDQUEwQjtJQUMxRSxNQUFNLENBQUNvQixVQUFVQyxZQUFZLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNLENBQUNzQixZQUFZQyxjQUFjLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUU3QyxVQUFVO0lBQ1YsTUFBTSxDQUFDd0IsU0FBU0MsV0FBVyxHQUFHekIsK0NBQVFBLENBQWtCO1FBQ3REMEIsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLFFBQVE7SUFDVjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRmpCLFdBQVc7WUFDWEUsU0FBUztZQUVULE1BQU1nQixTQUFTLE1BQU01QixtRUFBbUJBLENBQUNzQjtZQUN6Q2QsYUFBYW9CLE9BQU9DLFFBQVE7UUFDOUIsRUFBRSxPQUFPQyxLQUFLO1lBQ1psQixTQUFTa0IsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1FBQ2hELFNBQVU7WUFDUnRCLFdBQVc7UUFDYjtJQUNGO0lBRUE7O0dBRUMsR0FDRFgsZ0RBQVNBLENBQUM7UUFDUjRCO0lBQ0YsR0FBRztRQUFDTDtLQUFRO0lBRVo7O0dBRUMsR0FDRCxNQUFNVyxxQkFBcUIsQ0FBQ0MsS0FBNEJDO1FBQ3REWixXQUFXYSxDQUFBQSxPQUFTO2dCQUNsQixHQUFHQSxJQUFJO2dCQUNQLENBQUNGLElBQUksRUFBRUM7Z0JBQ1BYLE1BQU0sRUFBRSwwQ0FBMEM7WUFDcEQ7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTWEsdUJBQXVCLENBQUNDO1FBQzVCeEIscUJBQXFCc0IsQ0FBQUE7WUFDbkIsTUFBTUcsU0FBUyxJQUFJeEIsSUFBSXFCO1lBQ3ZCLElBQUlHLE9BQU9DLEdBQUcsQ0FBQ0YsYUFBYTtnQkFDMUJDLE9BQU9FLE1BQU0sQ0FBQ0g7WUFDaEIsT0FBTztnQkFDTEMsT0FBT0csR0FBRyxDQUFDSjtZQUNiO1lBQ0EsT0FBT0M7UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNSSxrQkFBa0I7UUFDdEIsSUFBSTlCLGtCQUFrQitCLElBQUksS0FBS3JDLFVBQVVzQyxNQUFNLEVBQUU7WUFDL0MvQixxQkFBcUIsSUFBSUM7UUFDM0IsT0FBTztZQUNMRCxxQkFBcUIsSUFBSUMsSUFBSVIsVUFBVXVDLEdBQUcsQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsRUFBRTtRQUN0RDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQyxtQkFBbUIsQ0FBQ0MsVUFBMkJDO1FBQ25EbEMsZ0JBQWdCO1lBQUVpQztZQUFVQztRQUFPO1FBQ25DaEMsWUFBWTtJQUNkO0lBRUE7O0dBRUMsR0FDRCxNQUFNaUMsb0JBQW9CO1FBQ3hCbkMsZ0JBQWdCO1FBQ2hCRSxZQUFZO0lBQ2Q7SUFFQTs7R0FFQyxHQUNELE1BQU1rQyx3QkFBd0I7UUFDNUIsSUFBSSxDQUFDckMsY0FBYztRQUVuQkssY0FBYztRQUNkLElBQUk7WUFDRixNQUFNcEIsK0RBQWVBLENBQUNlLGFBQWFrQyxRQUFRLENBQUNGLEVBQUUsRUFBRTtnQkFDOUN0QixRQUFRVixhQUFhbUMsTUFBTSxLQUFLLFlBQVksYUFBYTtnQkFDekRqQyxVQUFVQSxTQUFTb0MsSUFBSSxNQUFNQztZQUMvQjtZQUVBLG1CQUFtQjtZQUNuQixNQUFNNUI7WUFFTixrQkFBa0I7WUFDbEJiLHFCQUFxQixJQUFJQztZQUV6QixjQUFjO1lBQ2RxQztZQUVBLElBQUk5QyxxQkFBcUI7Z0JBQ3ZCQTtZQUNGO1FBQ0YsRUFBRSxPQUFPd0IsS0FBSztZQUNabEIsU0FBU2tCLGVBQWVDLFFBQVFELElBQUlFLE9BQU8sR0FBRztRQUNoRCxTQUFVO1lBQ1JYLGNBQWM7UUFDaEI7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTW1DLG9CQUFvQixPQUFPTDtRQUMvQixJQUFJdEMsa0JBQWtCK0IsSUFBSSxLQUFLLEdBQUc7UUFFbEN2QixjQUFjO1FBQ2QsSUFBSTtZQUNGLE1BQU1vQyxXQUFXQyxNQUFNQyxJQUFJLENBQUM5QyxtQkFBbUJpQyxHQUFHLENBQUNFLENBQUFBLEtBQ2pEL0MsK0RBQWVBLENBQUMrQyxJQUFJO29CQUNsQnRCLFFBQVF5QixXQUFXLFlBQVksYUFBYTtvQkFDNUNqQyxVQUFVLFFBQWUsT0FBUGlDLFFBQU87Z0JBQzNCO1lBR0YsTUFBTVMsUUFBUUMsR0FBRyxDQUFDSjtZQUVsQixtQkFBbUI7WUFDbkIsTUFBTTlCO1lBRU4sa0JBQWtCO1lBQ2xCYixxQkFBcUIsSUFBSUM7WUFFekIsSUFBSVQscUJBQXFCO2dCQUN2QkE7WUFDRjtRQUNGLEVBQUUsT0FBT3dCLEtBQUs7WUFDWmxCLFNBQVNrQixlQUFlQyxRQUFRRCxJQUFJRSxPQUFPLEdBQUc7UUFDaEQsU0FBVTtZQUNSWCxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU15QyxhQUFhLENBQUNDO1FBQ2xCLE9BQU8sSUFBSUMsS0FBS0QsWUFBWUUsa0JBQWtCLENBQUMsU0FBUztZQUN0REMsTUFBTTtZQUNOQyxPQUFPO1lBQ1BDLEtBQUs7WUFDTEMsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7SUFDRjtJQUVBLElBQUk3RCxTQUFTO1FBQ1gscUJBQ0UsOERBQUM4RDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBR0QsV0FBVTs7b0NBQXNDO29DQUM5QmpFLFVBQVVzQyxNQUFNO29DQUFDOzs7Ozs7OzRCQUl0Q2hDLGtCQUFrQitCLElBQUksR0FBRyxtQkFDeEIsOERBQUMyQjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNFO3dDQUNDQyxTQUFTLElBQU1uQixrQkFBa0I7d0NBQ2pDb0IsVUFBVXhEO3dDQUNWb0QsV0FBVTs7NENBQ1g7NENBQ1czRCxrQkFBa0IrQixJQUFJOzRDQUFDOzs7Ozs7O2tEQUVuQyw4REFBQzhCO3dDQUNDQyxTQUFTLElBQU1uQixrQkFBa0I7d0NBQ2pDb0IsVUFBVXhEO3dDQUNWb0QsV0FBVTs7NENBQ1g7NENBQ1UzRCxrQkFBa0IrQixJQUFJOzRDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU94Qyw4REFBQzJCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQ0MxQyxPQUFPYixRQUFRd0QsV0FBVyxJQUFJO2dDQUM5QkMsVUFBVSxDQUFDQyxJQUFNL0MsbUJBQW1CLGVBQWUrQyxFQUFFQyxNQUFNLENBQUM5QyxLQUFLLElBQUlvQjtnQ0FDckVpQixXQUFVOztrREFFViw4REFBQ1U7d0NBQU8vQyxPQUFNO2tEQUFHOzs7Ozs7a0RBQ2pCLDhEQUFDK0M7d0NBQU8vQyxPQUFNO2tEQUFhOzs7Ozs7a0RBQzNCLDhEQUFDK0M7d0NBQU8vQyxPQUFNO2tEQUFXOzs7Ozs7a0RBQ3pCLDhEQUFDK0M7d0NBQU8vQyxPQUFNO2tEQUFROzs7Ozs7Ozs7Ozs7MENBR3hCLDhEQUFDZ0Q7Z0NBQ0NDLE1BQUs7Z0NBQ0xqRCxPQUFPYixRQUFRK0QsU0FBUyxJQUFJO2dDQUM1Qk4sVUFBVSxDQUFDQyxJQUFNL0MsbUJBQW1CLGFBQWErQyxFQUFFQyxNQUFNLENBQUM5QyxLQUFLLElBQUlvQjtnQ0FDbkVpQixXQUFVO2dDQUNWYyxhQUFZOzs7Ozs7MENBR2QsOERBQUNIO2dDQUNDQyxNQUFLO2dDQUNMakQsT0FBT2IsUUFBUWlFLE9BQU8sSUFBSTtnQ0FDMUJSLFVBQVUsQ0FBQ0MsSUFBTS9DLG1CQUFtQixXQUFXK0MsRUFBRUMsTUFBTSxDQUFDOUMsS0FBSyxJQUFJb0I7Z0NBQ2pFaUIsV0FBVTtnQ0FDVmMsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTWpCM0UsdUJBQ0MsOERBQUM0RDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ2dCO29CQUFFaEIsV0FBVTs4QkFBd0I3RDs7Ozs7Ozs7Ozs7MEJBS3pDLDhEQUFDNEQ7Z0JBQUlDLFdBQVU7MEJBQ1pqRSxVQUFVc0MsTUFBTSxLQUFLLGtCQUNwQiw4REFBQzBCO29CQUFJQyxXQUFVOzhCQUFzQzs7Ozs7eUNBSXJELDhEQUFDaUI7b0JBQU1qQixXQUFVOztzQ0FDZiw4REFBQ2tCOzRCQUFNbEIsV0FBVTtzQ0FDZiw0RUFBQ21COztrREFDQyw4REFBQ0M7d0NBQUdwQixXQUFVO2tEQUNaLDRFQUFDVzs0Q0FDQ0MsTUFBSzs0Q0FDTFMsU0FBU2hGLGtCQUFrQitCLElBQUksS0FBS3JDLFVBQVVzQyxNQUFNLElBQUl0QyxVQUFVc0MsTUFBTSxHQUFHOzRDQUMzRWtDLFVBQVVwQzs0Q0FDVjZCLFdBQVU7Ozs7Ozs7Ozs7O2tEQUdkLDhEQUFDb0I7d0NBQUdwQixXQUFVO2tEQUFpRjs7Ozs7O2tEQUcvRiw4REFBQ29CO3dDQUFHcEIsV0FBVTtrREFBaUY7Ozs7OztrREFHL0YsOERBQUNvQjt3Q0FBR3BCLFdBQVU7a0RBQWlGOzs7Ozs7a0RBRy9GLDhEQUFDb0I7d0NBQUdwQixXQUFVO2tEQUFpRjs7Ozs7O2tEQUcvRiw4REFBQ29CO3dDQUFHcEIsV0FBVTtrREFBaUY7Ozs7OztrREFHL0YsOERBQUNvQjt3Q0FBR3BCLFdBQVU7a0RBQWlGOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLbkcsOERBQUNzQjs0QkFBTXRCLFdBQVU7c0NBQ2RqRSxVQUFVdUMsR0FBRyxDQUFDLENBQUNJLHlCQUNkLDhEQUFDeUM7b0NBQXFCbkIsV0FBVTs7c0RBQzlCLDhEQUFDdUI7NENBQUd2QixXQUFVO3NEQUNaLDRFQUFDVztnREFDQ0MsTUFBSztnREFDTFMsU0FBU2hGLGtCQUFrQjJCLEdBQUcsQ0FBQ1UsU0FBU0YsRUFBRTtnREFDMUMrQixVQUFVLElBQU0xQyxxQkFBcUJhLFNBQVNGLEVBQUU7Z0RBQ2hEd0IsV0FBVTs7Ozs7Ozs7Ozs7c0RBR2QsOERBQUN1Qjs0Q0FBR3ZCLFdBQVU7OzhEQUNaLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDWnRCLFNBQVM4QyxhQUFhLElBQUk7Ozs7Ozs4REFFN0IsOERBQUN6QjtvREFBSUMsV0FBVTs4REFDWnRCLFNBQVMrQyxjQUFjOzs7Ozs7Ozs7Ozs7c0RBRzVCLDhEQUFDRjs0Q0FBR3ZCLFdBQVU7c0RBQ1osNEVBQUMwQjtnREFBSzFCLFdBQVcsNERBQTJGLE9BQS9CdEUsbUVBQW1CQSxDQUFDOzBEQUM5RkMseUVBQXlCQSxDQUFDK0MsU0FBUzRCLFdBQVc7Ozs7Ozs7Ozs7O3NEQUduRCw4REFBQ2lCOzRDQUFHdkIsV0FBVTtzREFDWiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ1pwRSxpRUFBaUJBLENBQUM4QyxTQUFTNEIsV0FBVyxFQUFFNUIsU0FBU2lELFdBQVc7Ozs7Ozs7Ozs7O3NEQUdqRSw4REFBQ0o7NENBQUd2QixXQUFVO3NEQUNaLDRFQUFDRDtnREFBSUMsV0FBVTswREFDWnRCLFNBQVNrRCxNQUFNOzs7Ozs7Ozs7OztzREFHcEIsOERBQUNMOzRDQUFHdkIsV0FBVTtzREFDWFYsV0FBV1osU0FBU21ELFNBQVM7Ozs7OztzREFFaEMsOERBQUNOOzRDQUFHdkIsV0FBVTs7OERBQ1osOERBQUNFO29EQUNDQyxTQUFTLElBQU0xQixpQkFBaUJDLFVBQVU7b0RBQzFDc0IsV0FBVTs4REFDWDs7Ozs7OzhEQUdELDhEQUFDRTtvREFDQ0MsU0FBUyxJQUFNMUIsaUJBQWlCQyxVQUFVO29EQUMxQ3NCLFdBQVU7OERBQ1g7Ozs7Ozs7Ozs7Ozs7bUNBN0NJdEIsU0FBU0YsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBeUQ3QmhDLDhCQUNDLDhEQUFDdUQ7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUM4QjtnQ0FBRzlCLFdBQVU7O29DQUNYeEQsYUFBYW1DLE1BQU0sS0FBSyxZQUFZLFlBQVk7b0NBQVM7Ozs7Ozs7MENBRzVELDhEQUFDb0I7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDZ0I7d0NBQUVoQixXQUFVOzswREFDWCw4REFBQytCOzBEQUFPOzs7Ozs7NENBQWtCOzRDQUFFdkYsYUFBYWtDLFFBQVEsQ0FBQzhDLGFBQWE7Ozs7Ozs7a0RBRWpFLDhEQUFDUjt3Q0FBRWhCLFdBQVU7OzBEQUNYLDhEQUFDK0I7MERBQU87Ozs7Ozs0Q0FBYzs0Q0FBRXBHLHlFQUF5QkEsQ0FBQ2EsYUFBYWtDLFFBQVEsQ0FBQzRCLFdBQVc7Ozs7Ozs7a0RBRXJGLDhEQUFDVTt3Q0FBRWhCLFdBQVU7OzBEQUNYLDhEQUFDK0I7MERBQU87Ozs7Ozs0Q0FBZ0I7NENBQUV2RixhQUFha0MsUUFBUSxDQUFDa0QsTUFBTTs7Ozs7Ozs7Ozs7OzswQ0FJMUQsOERBQUM3QjtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNnQzt3Q0FBTWhDLFdBQVU7a0RBQStDOzs7Ozs7a0RBR2hFLDhEQUFDaUM7d0NBQ0N0RSxPQUFPakI7d0NBQ1A2RCxVQUFVLENBQUNDLElBQU03RCxZQUFZNkQsRUFBRUMsTUFBTSxDQUFDOUMsS0FBSzt3Q0FDM0N1RSxNQUFNO3dDQUNObEMsV0FBVTt3Q0FDVmMsYUFBWTs7Ozs7Ozs7Ozs7OzBDQUloQiw4REFBQ2Y7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRTt3Q0FDQ0MsU0FBU3ZCO3dDQUNUb0IsV0FBVTtrREFDWDs7Ozs7O2tEQUdELDhEQUFDRTt3Q0FDQ0MsU0FBU3RCO3dDQUNUdUIsVUFBVXhEO3dDQUNWb0QsV0FBVywyRUFJVixPQUhDeEQsYUFBYW1DLE1BQU0sS0FBSyxZQUNwQixvQ0FDQTtrREFHTC9CLGFBQWEsa0JBQW1CSixhQUFhbUMsTUFBTSxLQUFLLFlBQVksWUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNuRztHQWxad0I5QztLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9hcHByb3ZhbHMvTWFuYWdlckFwcHJvdmFsSW50ZXJmYWNlLnRzeD80ODI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBcbiAgZ2V0UGVuZGluZ0FwcHJvdmFscywgXG4gIHByb2Nlc3NBcHByb3ZhbCwgXG4gIEFwcHJvdmFsUmVxdWVzdCwgXG4gIEFwcHJvdmFsRmlsdGVycyxcbiAgZ2V0U3RhdHVzQmFkZ2VDb2xvcixcbiAgZ2V0UmVxdWVzdFR5cGVEaXNwbGF5TmFtZSxcbiAgZm9ybWF0UmVxdWVzdERhdGFcbn0gZnJvbSAnQC9saWIvYXBwcm92YWxzJztcblxuLyoqXG4gKiBQcm9wcyBmb3IgTWFuYWdlckFwcHJvdmFsSW50ZXJmYWNlIGNvbXBvbmVudFxuICovXG5pbnRlcmZhY2UgTWFuYWdlckFwcHJvdmFsSW50ZXJmYWNlUHJvcHMge1xuICBvbkFwcHJvdmFsUHJvY2Vzc2VkPzogKCkgPT4gdm9pZDtcbn1cblxuLyoqXG4gKiBQcm9jZXNzIGFwcHJvdmFsIG1vZGFsIGRhdGFcbiAqL1xuaW50ZXJmYWNlIFByb2Nlc3NNb2RhbERhdGEge1xuICBhcHByb3ZhbDogQXBwcm92YWxSZXF1ZXN0O1xuICBhY3Rpb246ICdhcHByb3ZlJyB8ICdyZWplY3QnO1xufVxuXG4vKipcbiAqIE1hbmFnZXJBcHByb3ZhbEludGVyZmFjZSBDb21wb25lbnRcbiAqIEludGVyZmFjZSBmb3IgbWFuYWdlcnMgdG8gdmlldywgYXBwcm92ZSwgYW5kIHJlamVjdCBwZW5kaW5nIGFwcHJvdmFsIHJlcXVlc3RzXG4gKi9cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hbmFnZXJBcHByb3ZhbEludGVyZmFjZSh7IG9uQXBwcm92YWxQcm9jZXNzZWQgfTogTWFuYWdlckFwcHJvdmFsSW50ZXJmYWNlUHJvcHMpIHtcbiAgY29uc3QgW2FwcHJvdmFscywgc2V0QXBwcm92YWxzXSA9IHVzZVN0YXRlPEFwcHJvdmFsUmVxdWVzdFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRBcHByb3ZhbHMsIHNldFNlbGVjdGVkQXBwcm92YWxzXSA9IHVzZVN0YXRlPFNldDxudW1iZXI+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbcHJvY2Vzc01vZGFsLCBzZXRQcm9jZXNzTW9kYWxdID0gdXNlU3RhdGU8UHJvY2Vzc01vZGFsRGF0YSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY29tbWVudHMsIHNldENvbW1lbnRzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Byb2Nlc3NpbmcsIHNldFByb2Nlc3NpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBcbiAgLy8gRmlsdGVyc1xuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZTxBcHByb3ZhbEZpbHRlcnM+KHtcbiAgICBwYWdlOiAxLFxuICAgIGxpbWl0OiAyMCxcbiAgICBzdGF0dXM6ICdwZW5kaW5nJ1xuICB9KTtcblxuICAvKipcbiAgICogTG9hZCBwZW5kaW5nIGFwcHJvdmFsc1xuICAgKi9cbiAgY29uc3QgbG9hZEFwcHJvdmFscyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBnZXRQZW5kaW5nQXBwcm92YWxzKGZpbHRlcnMpO1xuICAgICAgc2V0QXBwcm92YWxzKHJlc3VsdC5yZXF1ZXN0cyk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBsb2FkIGFwcHJvdmFscycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLyoqXG4gICAqIExvYWQgYXBwcm92YWxzIG9uIGNvbXBvbmVudCBtb3VudCBhbmQgZmlsdGVyIGNoYW5nZXNcbiAgICovXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZEFwcHJvdmFscygpO1xuICB9LCBbZmlsdGVyc10pO1xuXG4gIC8qKlxuICAgKiBIYW5kbGUgZmlsdGVyIGNoYW5nZXNcbiAgICovXG4gIGNvbnN0IGhhbmRsZUZpbHRlckNoYW5nZSA9IChrZXk6IGtleW9mIEFwcHJvdmFsRmlsdGVycywgdmFsdWU6IGFueSkgPT4ge1xuICAgIHNldEZpbHRlcnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIFtrZXldOiB2YWx1ZSxcbiAgICAgIHBhZ2U6IDEgLy8gUmVzZXQgdG8gZmlyc3QgcGFnZSB3aGVuIGZpbHRlcnMgY2hhbmdlXG4gICAgfSkpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBIYW5kbGUgaW5kaXZpZHVhbCBhcHByb3ZhbCBzZWxlY3Rpb25cbiAgICovXG4gIGNvbnN0IGhhbmRsZUFwcHJvdmFsU2VsZWN0ID0gKGFwcHJvdmFsSWQ6IG51bWJlcikgPT4ge1xuICAgIHNldFNlbGVjdGVkQXBwcm92YWxzKHByZXYgPT4ge1xuICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgIGlmIChuZXdTZXQuaGFzKGFwcHJvdmFsSWQpKSB7XG4gICAgICAgIG5ld1NldC5kZWxldGUoYXBwcm92YWxJZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBuZXdTZXQuYWRkKGFwcHJvdmFsSWQpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG5ld1NldDtcbiAgICB9KTtcbiAgfTtcblxuICAvKipcbiAgICogSGFuZGxlIHNlbGVjdCBhbGwgYXBwcm92YWxzXG4gICAqL1xuICBjb25zdCBoYW5kbGVTZWxlY3RBbGwgPSAoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkQXBwcm92YWxzLnNpemUgPT09IGFwcHJvdmFscy5sZW5ndGgpIHtcbiAgICAgIHNldFNlbGVjdGVkQXBwcm92YWxzKG5ldyBTZXQoKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFNlbGVjdGVkQXBwcm92YWxzKG5ldyBTZXQoYXBwcm92YWxzLm1hcChhID0+IGEuaWQpKSk7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiBPcGVuIHByb2Nlc3MgbW9kYWxcbiAgICovXG4gIGNvbnN0IG9wZW5Qcm9jZXNzTW9kYWwgPSAoYXBwcm92YWw6IEFwcHJvdmFsUmVxdWVzdCwgYWN0aW9uOiAnYXBwcm92ZScgfCAncmVqZWN0JykgPT4ge1xuICAgIHNldFByb2Nlc3NNb2RhbCh7IGFwcHJvdmFsLCBhY3Rpb24gfSk7XG4gICAgc2V0Q29tbWVudHMoJycpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBDbG9zZSBwcm9jZXNzIG1vZGFsXG4gICAqL1xuICBjb25zdCBjbG9zZVByb2Nlc3NNb2RhbCA9ICgpID0+IHtcbiAgICBzZXRQcm9jZXNzTW9kYWwobnVsbCk7XG4gICAgc2V0Q29tbWVudHMoJycpO1xuICB9O1xuXG4gIC8qKlxuICAgKiBQcm9jZXNzIHNpbmdsZSBhcHByb3ZhbFxuICAgKi9cbiAgY29uc3QgaGFuZGxlUHJvY2Vzc0FwcHJvdmFsID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghcHJvY2Vzc01vZGFsKSByZXR1cm47XG5cbiAgICBzZXRQcm9jZXNzaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBwcm9jZXNzQXBwcm92YWwocHJvY2Vzc01vZGFsLmFwcHJvdmFsLmlkLCB7XG4gICAgICAgIHN0YXR1czogcHJvY2Vzc01vZGFsLmFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ2FwcHJvdmVkJyA6ICdyZWplY3RlZCcsXG4gICAgICAgIGNvbW1lbnRzOiBjb21tZW50cy50cmltKCkgfHwgdW5kZWZpbmVkXG4gICAgICB9KTtcblxuICAgICAgLy8gUmVmcmVzaCB0aGUgbGlzdFxuICAgICAgYXdhaXQgbG9hZEFwcHJvdmFscygpO1xuICAgICAgXG4gICAgICAvLyBDbGVhciBzZWxlY3Rpb25cbiAgICAgIHNldFNlbGVjdGVkQXBwcm92YWxzKG5ldyBTZXQoKSk7XG4gICAgICBcbiAgICAgIC8vIENsb3NlIG1vZGFsXG4gICAgICBjbG9zZVByb2Nlc3NNb2RhbCgpO1xuICAgICAgXG4gICAgICBpZiAob25BcHByb3ZhbFByb2Nlc3NlZCkge1xuICAgICAgICBvbkFwcHJvdmFsUHJvY2Vzc2VkKCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBzZXRFcnJvcihlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBwcm9jZXNzIGFwcHJvdmFsJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFByb2Nlc3NpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvKipcbiAgICogUHJvY2VzcyBidWxrIGFwcHJvdmFsc1xuICAgKi9cbiAgY29uc3QgaGFuZGxlQnVsa1Byb2Nlc3MgPSBhc3luYyAoYWN0aW9uOiAnYXBwcm92ZScgfCAncmVqZWN0JykgPT4ge1xuICAgIGlmIChzZWxlY3RlZEFwcHJvdmFscy5zaXplID09PSAwKSByZXR1cm47XG5cbiAgICBzZXRQcm9jZXNzaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwcm9taXNlcyA9IEFycmF5LmZyb20oc2VsZWN0ZWRBcHByb3ZhbHMpLm1hcChpZCA9PlxuICAgICAgICBwcm9jZXNzQXBwcm92YWwoaWQsIHtcbiAgICAgICAgICBzdGF0dXM6IGFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ2FwcHJvdmVkJyA6ICdyZWplY3RlZCcsXG4gICAgICAgICAgY29tbWVudHM6IGBCdWxrICR7YWN0aW9ufWQgYnkgbWFuYWdlcmBcbiAgICAgICAgfSlcbiAgICAgICk7XG5cbiAgICAgIGF3YWl0IFByb21pc2UuYWxsKHByb21pc2VzKTtcblxuICAgICAgLy8gUmVmcmVzaCB0aGUgbGlzdFxuICAgICAgYXdhaXQgbG9hZEFwcHJvdmFscygpO1xuICAgICAgXG4gICAgICAvLyBDbGVhciBzZWxlY3Rpb25cbiAgICAgIHNldFNlbGVjdGVkQXBwcm92YWxzKG5ldyBTZXQoKSk7XG4gICAgICBcbiAgICAgIGlmIChvbkFwcHJvdmFsUHJvY2Vzc2VkKSB7XG4gICAgICAgIG9uQXBwcm92YWxQcm9jZXNzZWQoKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIHByb2Nlc3MgYnVsayBhcHByb3ZhbHMnKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0UHJvY2Vzc2luZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIC8qKlxuICAgKiBGb3JtYXQgZGF0ZSBmb3IgZGlzcGxheVxuICAgKi9cbiAgY29uc3QgZm9ybWF0RGF0ZSA9IChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gbmV3IERhdGUoZGF0ZVN0cmluZykudG9Mb2NhbGVEYXRlU3RyaW5nKCdlbi1VUycsIHtcbiAgICAgIHllYXI6ICdudW1lcmljJyxcbiAgICAgIG1vbnRoOiAnc2hvcnQnLFxuICAgICAgZGF5OiAnbnVtZXJpYycsXG4gICAgICBob3VyOiAnMi1kaWdpdCcsXG4gICAgICBtaW51dGU6ICcyLWRpZ2l0J1xuICAgIH0pO1xuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBpdGVtcy1jZW50ZXIgaC02NFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC02IHctNiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZFwiPlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgUGVuZGluZyBBcHByb3ZhbHMgKHthcHByb3ZhbHMubGVuZ3RofSlcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIFxuICAgICAgICAgIHsvKiBCdWxrIEFjdGlvbnMgKi99XG4gICAgICAgICAge3NlbGVjdGVkQXBwcm92YWxzLnNpemUgPiAwICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUJ1bGtQcm9jZXNzKCdhcHByb3ZlJyl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e3Byb2Nlc3Npbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0xIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSBiZy1ncmVlbi02MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmVlbi03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBBcHByb3ZlICh7c2VsZWN0ZWRBcHByb3ZhbHMuc2l6ZX0pXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlQnVsa1Byb2Nlc3MoJ3JlamVjdCcpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9jZXNzaW5nfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtd2hpdGUgYmctcmVkLTYwMCByb3VuZGVkLW1kIGhvdmVyOmJnLXJlZC03MDAgZGlzYWJsZWQ6b3BhY2l0eS01MFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBSZWplY3QgKHtzZWxlY3RlZEFwcHJvdmFscy5zaXplfSlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmlsdGVycyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGZsZXggZmxleC13cmFwIGdhcC00XCI+XG4gICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgdmFsdWU9e2ZpbHRlcnMucmVxdWVzdFR5cGUgfHwgJyd9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbHRlckNoYW5nZSgncmVxdWVzdFR5cGUnLCBlLnRhcmdldC52YWx1ZSB8fCB1bmRlZmluZWQpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCB0ZXh0LXNtXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+QWxsIFR5cGVzPC9vcHRpb24+XG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiY29ycmVjdGlvblwiPlRpbWUgQ29ycmVjdGlvbjwvb3B0aW9uPlxuICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm92ZXJ0aW1lXCI+T3ZlcnRpbWU8L29wdGlvbj5cbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJsZWF2ZVwiPkxlYXZlPC9vcHRpb24+XG4gICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgXG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5zdGFydERhdGUgfHwgJyd9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbHRlckNoYW5nZSgnc3RhcnREYXRlJywgZS50YXJnZXQudmFsdWUgfHwgdW5kZWZpbmVkKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgdGV4dC1zbVwiXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlN0YXJ0IERhdGVcIlxuICAgICAgICAgIC8+XG4gICAgICAgICAgXG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5lbmREYXRlIHx8ICcnfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWx0ZXJDaGFuZ2UoJ2VuZERhdGUnLCBlLnRhcmdldC52YWx1ZSB8fCB1bmRlZmluZWQpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCB0ZXh0LXNtXCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRW5kIERhdGVcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFcnJvciBNZXNzYWdlICovfVxuICAgICAge2Vycm9yICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJteC02IG10LTQgcC0zIGJnLXJlZC01MCBib3JkZXIgYm9yZGVyLXJlZC0yMDAgcm91bmRlZC1tZFwiPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7LyogQXBwcm92YWxzIExpc3QgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgICB7YXBwcm92YWxzLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTYgcHktOCB0ZXh0LWNlbnRlciB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICBObyBwZW5kaW5nIGFwcHJvdmFscyBmb3VuZC5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDx0aGVhZCBjbGFzc05hbWU9XCJiZy1ncmF5LTUwXCI+XG4gICAgICAgICAgICAgIDx0cj5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdFwiPlxuICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e3NlbGVjdGVkQXBwcm92YWxzLnNpemUgPT09IGFwcHJvdmFscy5sZW5ndGggJiYgYXBwcm92YWxzLmxlbmd0aCA+IDB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVTZWxlY3RBbGx9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIHRleHQtYmx1ZS02MDAgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgRW1wbG95ZWVcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIFR5cGVcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIFJlcXVlc3QgRGV0YWlsc1xuICAgICAgICAgICAgICAgIDwvdGg+XG4gICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTYgcHktMyB0ZXh0LWxlZnQgdGV4dC14cyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNTAwIHVwcGVyY2FzZSB0cmFja2luZy13aWRlclwiPlxuICAgICAgICAgICAgICAgICAgUmVhc29uXG4gICAgICAgICAgICAgICAgPC90aD5cbiAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNiBweS0zIHRleHQtbGVmdCB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS01MDAgdXBwZXJjYXNlIHRyYWNraW5nLXdpZGVyXCI+XG4gICAgICAgICAgICAgICAgICBTdWJtaXR0ZWRcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC02IHB5LTMgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTUwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXJcIj5cbiAgICAgICAgICAgICAgICAgIEFjdGlvbnNcbiAgICAgICAgICAgICAgICA8L3RoPlxuICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgPC90aGVhZD5cbiAgICAgICAgICAgIDx0Ym9keSBjbGFzc05hbWU9XCJiZy13aGl0ZSBkaXZpZGUteSBkaXZpZGUtZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAge2FwcHJvdmFscy5tYXAoKGFwcHJvdmFsKSA9PiAoXG4gICAgICAgICAgICAgICAgPHRyIGtleT17YXBwcm92YWwuaWR9IGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZEFwcHJvdmFscy5oYXMoYXBwcm92YWwuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoKSA9PiBoYW5kbGVBcHByb3ZhbFNlbGVjdChhcHByb3ZhbC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicm91bmRlZCBib3JkZXItZ3JheS0zMDAgdGV4dC1ibHVlLTYwMCBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLnJlcXVlc3Rlck5hbWUgfHwgJ1Vua25vd24nfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7YXBwcm92YWwucmVxdWVzdGVyRW1haWx9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgaW5saW5lLWZsZXggcHgtMiBweS0xIHRleHQteHMgZm9udC1zZW1pYm9sZCByb3VuZGVkLWZ1bGwgJHtnZXRTdGF0dXNCYWRnZUNvbG9yKCdwZW5kaW5nJyl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFJlcXVlc3RUeXBlRGlzcGxheU5hbWUoYXBwcm92YWwucmVxdWVzdFR5cGUpfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRSZXF1ZXN0RGF0YShhcHByb3ZhbC5yZXF1ZXN0VHlwZSwgYXBwcm92YWwucmVxdWVzdERhdGEpfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktOTAwIG1heC13LXhzIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2FwcHJvdmFsLnJlYXNvbn1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcCB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAge2Zvcm1hdERhdGUoYXBwcm92YWwuY3JlYXRlZEF0KX1cbiAgICAgICAgICAgICAgICAgIDwvdGQ+XG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwIHRleHQtc20gZm9udC1tZWRpdW0gc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuUHJvY2Vzc01vZGFsKGFwcHJvdmFsLCAnYXBwcm92ZScpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGhvdmVyOnRleHQtZ3JlZW4tOTAwXCJcbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIEFwcHJvdmVcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuUHJvY2Vzc01vZGFsKGFwcHJvdmFsLCAncmVqZWN0Jyl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGhvdmVyOnRleHQtcmVkLTkwMFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICBSZWplY3RcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8L3RkPlxuICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC90Ym9keT5cbiAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcm9jZXNzIE1vZGFsICovfVxuICAgICAge3Byb2Nlc3NNb2RhbCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ncmF5LTYwMCBiZy1vcGFjaXR5LTUwIG92ZXJmbG93LXktYXV0byBoLWZ1bGwgdy1mdWxsIHotNTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHRvcC0yMCBteC1hdXRvIHAtNSBib3JkZXIgdy05NiBzaGFkb3ctbGcgcm91bmRlZC1tZCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0zXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIHtwcm9jZXNzTW9kYWwuYWN0aW9uID09PSAnYXBwcm92ZScgPyAnQXBwcm92ZScgOiAnUmVqZWN0J30gUmVxdWVzdFxuICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00IHAtMyBiZy1ncmF5LTUwIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgIDxzdHJvbmc+RW1wbG95ZWU6PC9zdHJvbmc+IHtwcm9jZXNzTW9kYWwuYXBwcm92YWwucmVxdWVzdGVyTmFtZX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICA8c3Ryb25nPlR5cGU6PC9zdHJvbmc+IHtnZXRSZXF1ZXN0VHlwZURpc3BsYXlOYW1lKHByb2Nlc3NNb2RhbC5hcHByb3ZhbC5yZXF1ZXN0VHlwZSl9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICAgICAgPHN0cm9uZz5SZWFzb246PC9zdHJvbmc+IHtwcm9jZXNzTW9kYWwuYXBwcm92YWwucmVhc29ufVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICBDb21tZW50cyAoT3B0aW9uYWwpXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb21tZW50c31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29tbWVudHMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkFkZCBhbnkgY29tbWVudHMuLi5cIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZVByb2Nlc3NNb2RhbH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGhvdmVyOmJnLWdyYXktNTBcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVByb2Nlc3NBcHByb3ZhbH1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9jZXNzaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtNCBweS0yIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZSByb3VuZGVkLW1kIGRpc2FibGVkOm9wYWNpdHktNTAgJHtcbiAgICAgICAgICAgICAgICAgICAgcHJvY2Vzc01vZGFsLmFjdGlvbiA9PT0gJ2FwcHJvdmUnXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JlZW4tNjAwIGhvdmVyOmJnLWdyZWVuLTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdiZy1yZWQtNjAwIGhvdmVyOmJnLXJlZC03MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7cHJvY2Vzc2luZyA/ICdQcm9jZXNzaW5nLi4uJyA6IChwcm9jZXNzTW9kYWwuYWN0aW9uID09PSAnYXBwcm92ZScgPyAnQXBwcm92ZScgOiAnUmVqZWN0Jyl9XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiZ2V0UGVuZGluZ0FwcHJvdmFscyIsInByb2Nlc3NBcHByb3ZhbCIsImdldFN0YXR1c0JhZGdlQ29sb3IiLCJnZXRSZXF1ZXN0VHlwZURpc3BsYXlOYW1lIiwiZm9ybWF0UmVxdWVzdERhdGEiLCJNYW5hZ2VyQXBwcm92YWxJbnRlcmZhY2UiLCJvbkFwcHJvdmFsUHJvY2Vzc2VkIiwiYXBwcm92YWxzIiwic2V0QXBwcm92YWxzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwic2VsZWN0ZWRBcHByb3ZhbHMiLCJzZXRTZWxlY3RlZEFwcHJvdmFscyIsIlNldCIsInByb2Nlc3NNb2RhbCIsInNldFByb2Nlc3NNb2RhbCIsImNvbW1lbnRzIiwic2V0Q29tbWVudHMiLCJwcm9jZXNzaW5nIiwic2V0UHJvY2Vzc2luZyIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwicGFnZSIsImxpbWl0Iiwic3RhdHVzIiwibG9hZEFwcHJvdmFscyIsInJlc3VsdCIsInJlcXVlc3RzIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwiaGFuZGxlRmlsdGVyQ2hhbmdlIiwia2V5IiwidmFsdWUiLCJwcmV2IiwiaGFuZGxlQXBwcm92YWxTZWxlY3QiLCJhcHByb3ZhbElkIiwibmV3U2V0IiwiaGFzIiwiZGVsZXRlIiwiYWRkIiwiaGFuZGxlU2VsZWN0QWxsIiwic2l6ZSIsImxlbmd0aCIsIm1hcCIsImEiLCJpZCIsIm9wZW5Qcm9jZXNzTW9kYWwiLCJhcHByb3ZhbCIsImFjdGlvbiIsImNsb3NlUHJvY2Vzc01vZGFsIiwiaGFuZGxlUHJvY2Vzc0FwcHJvdmFsIiwidHJpbSIsInVuZGVmaW5lZCIsImhhbmRsZUJ1bGtQcm9jZXNzIiwicHJvbWlzZXMiLCJBcnJheSIsImZyb20iLCJQcm9taXNlIiwiYWxsIiwiZm9ybWF0RGF0ZSIsImRhdGVTdHJpbmciLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiaG91ciIsIm1pbnV0ZSIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwic2VsZWN0IiwicmVxdWVzdFR5cGUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJvcHRpb24iLCJpbnB1dCIsInR5cGUiLCJzdGFydERhdGUiLCJwbGFjZWhvbGRlciIsImVuZERhdGUiLCJwIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJjaGVja2VkIiwidGJvZHkiLCJ0ZCIsInJlcXVlc3Rlck5hbWUiLCJyZXF1ZXN0ZXJFbWFpbCIsInNwYW4iLCJyZXF1ZXN0RGF0YSIsInJlYXNvbiIsImNyZWF0ZWRBdCIsImgzIiwic3Ryb25nIiwibGFiZWwiLCJ0ZXh0YXJlYSIsInJvd3MiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/approvals/ManagerApprovalInterface.tsx\n"));

/***/ })

});