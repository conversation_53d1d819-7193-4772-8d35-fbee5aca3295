/**
 * Fix foreign key constraints in approvals table
 */

const mysql = require('mysql2/promise');
require('dotenv').config({ path: './backend/.env' });

async function fixForeignKeys() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'flexair_timekeeping'
    });

    console.log('📊 Checking current foreign key constraints...');
    const [constraints] = await connection.execute(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'approvals' 
      AND TABLE_SCHEMA = DATABASE()
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);
    
    console.log('Current constraints:');
    console.table(constraints);

    // Drop old foreign key constraints
    console.log('\n🗑️  Dropping old foreign key constraints...');
    
    for (const constraint of constraints) {
      try {
        console.log(`Dropping constraint: ${constraint.CONSTRAINT_NAME}`);
        await connection.execute(`ALTER TABLE approvals DROP FOREIGN KEY ${constraint.CONSTRAINT_NAME}`);
      } catch (error) {
        console.warn(`Warning: Could not drop constraint ${constraint.CONSTRAINT_NAME}:`, error.message);
      }
    }

    // Add correct foreign key constraints
    console.log('\n➕ Adding correct foreign key constraints...');
    
    try {
      await connection.execute(`
        ALTER TABLE approvals 
        ADD CONSTRAINT fk_approvals_time_log 
        FOREIGN KEY (time_log_id) REFERENCES time_logs(id) ON DELETE CASCADE
      `);
      console.log('✅ Added time_log_id constraint');
    } catch (error) {
      console.warn('Warning:', error.message);
    }

    try {
      await connection.execute(`
        ALTER TABLE approvals 
        ADD CONSTRAINT fk_approvals_requested_by 
        FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE
      `);
      console.log('✅ Added requested_by constraint');
    } catch (error) {
      console.warn('Warning:', error.message);
    }

    try {
      await connection.execute(`
        ALTER TABLE approvals 
        ADD CONSTRAINT fk_approvals_assigned_to 
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE
      `);
      console.log('✅ Added assigned_to constraint');
    } catch (error) {
      console.warn('Warning:', error.message);
    }

    try {
      await connection.execute(`
        ALTER TABLE approvals 
        ADD CONSTRAINT fk_approvals_approved_by 
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
      `);
      console.log('✅ Added approved_by constraint');
    } catch (error) {
      console.warn('Warning:', error.message);
    }

    console.log('\n📊 Checking updated constraints...');
    const [newConstraints] = await connection.execute(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'approvals' 
      AND TABLE_SCHEMA = DATABASE()
      AND REFERENCED_TABLE_NAME IS NOT NULL
    `);
    
    console.log('Updated constraints:');
    console.table(newConstraints);

    console.log('\n✅ Foreign key constraints fixed successfully!');

  } catch (error) {
    console.error('❌ Failed to fix foreign keys:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

fixForeignKeys();
