/**
 * Approval workflow service layer
 * Handles approval requests and manager approvals
 */

const { query } = require('../../shared/database');
const { logger } = require('../../shared/logger');

/**
 * Create approval request
 * @param {Object} requestData - Request data
 * @returns {Object} Created approval request
 */
const createApprovalRequest = async (requestData) => {
  try {
    const { 
      timeLogId, 
      requestedBy, 
      requestType, 
      requestData: reqData, 
      reason 
    } = requestData;

    // Verify time log exists
    const timeLogs = await query(
      'SELECT id, user_id FROM time_logs WHERE id = ?',
      [timeLogId]
    );

    if (timeLogs.length === 0) {
      throw new Error('Time log not found');
    }

    const timeLog = timeLogs[0];

    // Check if user can request approval for this time log
    if (timeLog.user_id !== requestedBy) {
      throw new Error('Can only request approval for your own time logs');
    }

    // Check if there's already a pending approval
    const existingApprovals = await query(
      'SELECT id FROM approvals WHERE time_log_id = ? AND status = "pending"',
      [timeLogId]
    );

    if (existingApprovals.length > 0) {
      throw new Error('There is already a pending approval for this time log');
    }

    // Find appropriate manager/admin to assign to
    const managers = await query(
      'SELECT id FROM users WHERE role IN ("admin", "manager") AND is_active = true ORDER BY role DESC LIMIT 1'
    );

    if (managers.length === 0) {
      throw new Error('No active managers available for approval');
    }

    const assignedTo = managers[0].id;

    // Create approval request
    const result = await query(
      `INSERT INTO approvals (time_log_id, requested_by, assigned_to, request_type, request_data, reason, status, created_at)
       VALUES (?, ?, ?, ?, ?, ?, 'pending', NOW())`,
      [timeLogId, requestedBy, assignedTo, requestType, JSON.stringify(reqData), reason]
    );

    const approvalId = result.insertId;

    // Log the action
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'CREATE', 'approval', ?, ?)`,
      [requestedBy, approvalId, JSON.stringify({ timeLogId, requestType, reason })]
    );

    // Return the created approval
    return await getApprovalById(approvalId);
  } catch (error) {
    logger.error('Error creating approval request:', error);
    throw error;
  }
};

/**
 * Process approval (approve/reject)
 * @param {number} approvalId - Approval ID
 * @param {number} approverId - Manager/Admin ID
 * @param {string} status - 'approved' or 'rejected'
 * @param {string} comments - Optional comments
 * @returns {Object} Updated approval
 */
const processApproval = async (approvalId, approverId, status, comments = null) => {
  try {
    // Get approval details
    const approval = await getApprovalById(approvalId);
    if (!approval) {
      throw new Error('Approval request not found');
    }

    if (approval.status !== 'pending') {
      throw new Error('Approval request has already been processed');
    }

    // Verify approver has permission
    const approver = await query(
      'SELECT id, role FROM users WHERE id = ? AND role IN ("admin", "manager")',
      [approverId]
    );

    if (approver.length === 0) {
      throw new Error('Only managers and admins can process approvals');
    }

    // Update approval status
    await query(
      `UPDATE approvals 
       SET status = ?, approved_by = ?, approved_at = NOW(), comments = ?
       WHERE id = ?`,
      [status, approverId, comments, approvalId]
    );

    // If approved and it's a correction request, apply the changes
    if (status === 'approved' && approval.request_type === 'correction') {
      try {
        const requestData = JSON.parse(approval.request_data);
        await applyTimeLogCorrection(approval.time_log_id, requestData, approverId);
      } catch (error) {
        logger.error('Error applying time log correction:', error);
        // Update approval to indicate correction failed
        await query(
          'UPDATE approvals SET comments = ? WHERE id = ?',
          [`${comments || ''} [Correction failed: ${error.message}]`, approvalId]
        );
      }
    }

    // Log the action
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'UPDATE', 'approval', ?, ?)`,
      [approverId, approvalId, JSON.stringify({ status, comments })]
    );

    // Return updated approval
    return await getApprovalById(approvalId);
  } catch (error) {
    logger.error('Error processing approval:', error);
    throw error;
  }
};

/**
 * Apply time log correction
 * @param {number} timeLogId - Time log ID
 * @param {Object} correctionData - Correction data
 * @param {number} correctedBy - ID of approver
 */
const applyTimeLogCorrection = async (timeLogId, correctionData, correctedBy) => {
  try {
    const updateFields = [];
    const params = [];

    // Handle allowed corrections - map frontend field names to database column names
    const fieldMapping = {
      'clock_in_time': 'clock_in',
      'clock_out_time': 'clock_out',
      'notes': 'notes'
    };

    Object.keys(fieldMapping).forEach(frontendField => {
      if (correctionData[frontendField] !== undefined) {
        const dbField = fieldMapping[frontendField];
        updateFields.push(`${dbField} = ?`);
        params.push(correctionData[frontendField]);
      }
    });

    if (updateFields.length === 0) {
      return; // No changes to apply
    }

    // Recalculate hours if times were corrected
    if (correctionData.clock_in_time || correctionData.clock_out_time) {
      // Get current time log
      const currentLog = await query('SELECT * FROM time_logs WHERE id = ?', [timeLogId]);
      if (currentLog.length > 0) {
        const log = currentLog[0];
        const clockInTime = new Date(correctionData.clock_in_time || log.clock_in);
        const clockOutTime = new Date(correctionData.clock_out_time || log.clock_out);
        
        if (clockOutTime && clockInTime) {
          const hoursWorked = (clockOutTime - clockInTime) / (1000 * 60 * 60);
          updateFields.push('total_hours = ?');
          params.push(hoursWorked.toFixed(2));
        }
      }
    }

    params.push(timeLogId);
    const updateQuery = `UPDATE time_logs SET ${updateFields.join(', ')} WHERE id = ?`;
    await query(updateQuery, params);

    // Log the correction
    await query(
      `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, new_values)
       VALUES (?, 'CORRECT', 'time_log', ?, ?)`,
      [correctedBy, timeLogId, JSON.stringify(correctionData)]
    );
  } catch (error) {
    logger.error('Error applying time log correction:', error);
    throw error;
  }
};

/**
 * Get approval by ID
 * @param {number} approvalId - Approval ID
 * @returns {Object|null} Approval data
 */
const getApprovalById = async (approvalId) => {
  try {
    const approvals = await query(
      `SELECT
        a.*,
        tl.clock_in as clock_in_time,
        tl.clock_out as clock_out_time,
        tl.total_hours,
        requester.first_name as requester_first_name,
        requester.last_name as requester_last_name,
        requester.email as requester_email,
        approver.first_name as approver_first_name,
        approver.last_name as approver_last_name,
        approver.email as approver_email
      FROM approvals a
      JOIN time_logs tl ON a.time_log_id = tl.id
      JOIN users requester ON a.requested_by = requester.id
      LEFT JOIN users approver ON a.approved_by = approver.id
      WHERE a.id = ?`,
      [approvalId]
    );

    if (approvals.length === 0) {
      return null;
    }

    const approval = approvals[0];
    
    // Parse request_data if it exists
    if (approval.request_data) {
      try {
        approval.request_data = JSON.parse(approval.request_data);
      } catch (error) {
        logger.warn('Failed to parse request_data for approval:', approvalId);
      }
    }

    return approval;
  } catch (error) {
    logger.error('Error getting approval by ID:', error);
    throw new Error('Failed to retrieve approval');
  }
};

/**
 * Get approvals with filtering and pagination
 * @param {Object} filters - Filter options
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Object} Approvals with pagination info
 */
const getApprovals = async (filters = {}, page = 1, limit = 20) => {
  try {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE 1=1';
    const params = [];

    // Apply filters
    if (filters.status) {
      whereClause += ' AND a.status = ?';
      params.push(filters.status);
    }

    if (filters.requestType) {
      whereClause += ' AND a.request_type = ?';
      params.push(filters.requestType);
    }

    if (filters.requestedBy) {
      whereClause += ' AND a.requested_by = ?';
      params.push(filters.requestedBy);
    }

    if (filters.assignedTo) {
      whereClause += ' AND a.assigned_to = ?';
      params.push(filters.assignedTo);
    }

    if (filters.startDate) {
      whereClause += ' AND DATE(a.created_at) >= ?';
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      whereClause += ' AND DATE(a.created_at) <= ?';
      params.push(filters.endDate);
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM approvals a ${whereClause}`;
    const countResult = await query(countQuery, params);
    const total = countResult[0].total;

    // Get approvals
    const approvalQuery = `
      SELECT
        a.*,
        tl.clock_in as clock_in_time,
        tl.clock_out as clock_out_time,
        tl.total_hours,
        requester.first_name as requester_first_name,
        requester.last_name as requester_last_name,
        requester.email as requester_email,
        approver.first_name as approver_first_name,
        approver.last_name as approver_last_name,
        approver.email as approver_email
      FROM approvals a
      JOIN time_logs tl ON a.time_log_id = tl.id
      JOIN users requester ON a.requested_by = requester.id
      LEFT JOIN users approver ON a.approved_by = approver.id
      ${whereClause}
      ORDER BY a.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const approvals = await query(approvalQuery, [...params, limit, offset]);

    // Parse request_data for each approval
    approvals.forEach(approval => {
      if (approval.request_data) {
        try {
          approval.request_data = JSON.parse(approval.request_data);
        } catch (error) {
          logger.warn('Failed to parse request_data for approval:', approval.id);
        }
      }
    });

    return {
      approvals,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  } catch (error) {
    logger.error('Error getting approvals:', error);
    throw new Error('Failed to retrieve approvals');
  }
};

/**
 * Get approval statistics
 * @param {Object} filters - Filter options
 * @returns {Object} Approval statistics
 */
const getApprovalStats = async (filters = {}) => {
  try {
    let whereClause = 'WHERE 1=1';
    const params = [];

    if (filters.startDate) {
      whereClause += ' AND DATE(created_at) >= ?';
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      whereClause += ' AND DATE(created_at) <= ?';
      params.push(filters.endDate);
    }

    const stats = await query(`
      SELECT 
        COUNT(*) as total_requests,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
        SUM(CASE WHEN request_type = 'correction' THEN 1 ELSE 0 END) as correction_requests,
        SUM(CASE WHEN request_type = 'approval' THEN 1 ELSE 0 END) as approval_requests,
        AVG(CASE WHEN approved_at IS NOT NULL 
            THEN TIMESTAMPDIFF(HOUR, created_at, approved_at) 
            ELSE NULL END) as avg_processing_time_hours
      FROM approvals
      ${whereClause}
    `, params);

    return stats[0];
  } catch (error) {
    logger.error('Error getting approval stats:', error);
    throw new Error('Failed to retrieve approval statistics');
  }
};

/**
 * Get user's own approval requests
 * @param {number} userId - User ID
 * @param {Object} filters - Filter options
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Object} User's approval requests with pagination info
 */
const getMyApprovalRequests = async (userId, filters = {}, page = 1, limit = 20) => {
  try {
    // Add user filter to existing filters
    const userFilters = { ...filters, requestedBy: userId };
    return await getApprovals(userFilters, page, limit);
  } catch (error) {
    logger.error('Error getting user approval requests:', error);
    throw new Error('Failed to retrieve approval requests');
  }
};

/**
 * Get pending approvals for managers
 * @param {Object} filters - Filter options
 * @param {number} page - Page number
 * @param {number} limit - Results per page
 * @returns {Object} Pending approvals with pagination info
 */
const getPendingApprovals = async (filters = {}, page = 1, limit = 20) => {
  try {
    // Add pending status filter
    const pendingFilters = { ...filters, status: 'pending' };
    return await getApprovals(pendingFilters, page, limit);
  } catch (error) {
    logger.error('Error getting pending approvals:', error);
    throw new Error('Failed to retrieve pending approvals');
  }
};

module.exports = {
  createApprovalRequest,
  processApproval,
  getApprovalById,
  getApprovals,
  getMyApprovalRequests,
  getPendingApprovals,
  getApprovalStats
};