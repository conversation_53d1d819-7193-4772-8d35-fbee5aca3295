'use client';

import React, { useState } from 'react';
import { createApprovalRequest, CreateApprovalData } from '@/lib/approvals';

/**
 * Props for ApprovalRequestForm component
 */
interface ApprovalRequestFormProps {
  timeLogId: number;
  currentClockIn?: string;
  currentClockOut?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form data interface
 */
interface FormData {
  requestType: 'correction' | 'overtime' | 'leave';
  reason: string;
  clockIn: string;
  clockOut: string;
  overtimeHours: string;
  overtimeDate: string;
  leaveStartDate: string;
  leaveEndDate: string;
  leaveType: string;
}

/**
 * ApprovalRequestForm Component
 * Form for employees to submit approval requests for time corrections, overtime, or leave
 */
export default function ApprovalRequestForm({
  timeLogId,
  currentClockIn = '',
  currentClockOut = '',
  onSuccess,
  onCancel
}: ApprovalRequestFormProps) {
  const [formData, setFormData] = useState<FormData>({
    requestType: 'correction',
    reason: '',
    clockIn: currentClockIn,
    clockOut: currentClockOut,
    overtimeHours: '',
    overtimeDate: '',
    leaveStartDate: '',
    leaveEndDate: '',
    leaveType: 'sick'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handle form field changes
   */
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  /**
   * Validate form data
   */
  const validateForm = (): string | null => {
    if (!formData.reason.trim()) {
      return 'Reason is required';
    }

    if (formData.requestType === 'correction') {
      if (!formData.clockIn) {
        return 'Clock in time is required for corrections';
      }
      if (!formData.clockOut) {
        return 'Clock out time is required for corrections';
      }
    }

    if (formData.requestType === 'overtime') {
      if (!formData.overtimeHours || parseFloat(formData.overtimeHours) <= 0) {
        return 'Valid overtime hours are required';
      }
      if (!formData.overtimeDate) {
        return 'Overtime date is required';
      }
    }

    if (formData.requestType === 'leave') {
      if (!formData.leaveStartDate) {
        return 'Leave start date is required';
      }
      if (!formData.leaveEndDate) {
        return 'Leave end date is required';
      }
      if (new Date(formData.leaveStartDate) > new Date(formData.leaveEndDate)) {
        return 'Leave start date must be before end date';
      }
    }

    return null;
  };

  /**
   * Prepare request data based on request type
   */
  const prepareRequestData = () => {
    switch (formData.requestType) {
      case 'correction':
        return {
          clockIn: formData.clockIn,
          clockOut: formData.clockOut
        };
      case 'overtime':
        return {
          hours: parseFloat(formData.overtimeHours),
          date: formData.overtimeDate
        };
      case 'leave':
        return {
          startDate: formData.leaveStartDate,
          endDate: formData.leaveEndDate,
          type: formData.leaveType
        };
      default:
        return {};
    }
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const requestData: CreateApprovalData = {
        timeLogId,
        requestType: formData.requestType,
        requestData: prepareRequestData(),
        reason: formData.reason.trim()
      };

      await createApprovalRequest(requestData);
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit approval request');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="compact-card">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-900">
          Request Approval
        </h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {error && (
        <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Request Type */}
        <div>
          <label htmlFor="requestType" className="block text-sm font-medium text-gray-700 mb-2">
            Request Type
          </label>
          <select
            id="requestType"
            name="requestType"
            value={formData.requestType}
            onChange={handleChange}
            className="form-input"
          >
            <option value="correction">Time Correction</option>
            <option value="overtime">Overtime Request</option>
            <option value="leave">Leave Request</option>
          </select>
        </div>

        {/* Time Correction Fields */}
        {formData.requestType === 'correction' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label htmlFor="clockIn" className="block text-sm font-medium text-gray-700 mb-2">
                Corrected Clock In Time
              </label>
              <input
                type="datetime-local"
                id="clockIn"
                name="clockIn"
                value={formData.clockIn}
                onChange={handleChange}
                className="form-input"
              />
            </div>
            <div>
              <label htmlFor="clockOut" className="block text-sm font-medium text-gray-700 mb-2">
                Corrected Clock Out Time
              </label>
              <input
                type="datetime-local"
                id="clockOut"
                name="clockOut"
                value={formData.clockOut}
                onChange={handleChange}
                className="form-input"
              />
            </div>
          </div>
        )}

        {/* Overtime Fields */}
        {formData.requestType === 'overtime' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="overtimeHours" className="block text-sm font-medium text-gray-700 mb-2">
                Overtime Hours
              </label>
              <input
                type="number"
                id="overtimeHours"
                name="overtimeHours"
                value={formData.overtimeHours}
                onChange={handleChange}
                min="0"
                step="0.5"
                placeholder="e.g., 2.5"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label htmlFor="overtimeDate" className="block text-sm font-medium text-gray-700 mb-2">
                Overtime Date
              </label>
              <input
                type="date"
                id="overtimeDate"
                name="overtimeDate"
                value={formData.overtimeDate}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}

        {/* Leave Fields */}
        {formData.requestType === 'leave' && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="leaveStartDate" className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date
                </label>
                <input
                  type="date"
                  id="leaveStartDate"
                  name="leaveStartDate"
                  value={formData.leaveStartDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label htmlFor="leaveEndDate" className="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  id="leaveEndDate"
                  name="leaveEndDate"
                  value={formData.leaveEndDate}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div>
              <label htmlFor="leaveType" className="block text-sm font-medium text-gray-700 mb-2">
                Leave Type
              </label>
              <select
                id="leaveType"
                name="leaveType"
                value={formData.leaveType}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="sick">Sick Leave</option>
                <option value="vacation">Vacation</option>
                <option value="personal">Personal Leave</option>
                <option value="emergency">Emergency Leave</option>
              </select>
            </div>
          </div>
        )}

        {/* Reason */}
        <div>
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
            Reason for Request <span className="text-red-500">*</span>
          </label>
          <textarea
            id="reason"
            name="reason"
            value={formData.reason}
            onChange={handleChange}
            rows={4}
            placeholder="Please provide a detailed reason for this request..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          />
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="btn btn-outline"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn btn-primary"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </button>
        </div>
      </form>
    </div>
  );
}
