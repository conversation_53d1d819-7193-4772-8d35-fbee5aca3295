/**
 * Test the time tracking service directly
 */

require('dotenv').config({ path: './backend/.env' });

async function testTimeService() {
  try {
    console.log('🧪 Testing time tracking service...\n');

    // Test database connection first
    const { query } = require('./backend/src/shared/database');
    
    console.log('1. Testing time_logs table structure...');
    const structure = await query('DESCRIBE time_logs');
    console.log('✅ Time logs table structure:');
    console.table(structure);

    console.log('\n2. Testing time service functions...');
    const timeService = require('./backend/src/modules/time/timeService');
    
    // Test getTimeLogs
    console.log('Testing getTimeLogs...');
    const timeLogs = await timeService.getTimeLogs({}, 1, 5);
    console.log('✅ getTimeLogs successful:', {
      count: timeLogs.timeLogs.length,
      pagination: timeLogs.pagination
    });

    // Test getTimeLogById if we have any logs
    if (timeLogs.timeLogs.length > 0) {
      const firstLogId = timeLogs.timeLogs[0].id;
      console.log('\nTesting getTimeLogById...');
      const timeLog = await timeService.getTimeLogById(firstLogId);
      console.log('✅ getTimeLogById successful:', {
        id: timeLog.id,
        user_id: timeLog.user_id,
        total_hours: timeLog.total_hours,
        status: timeLog.status
      });
    }

    console.log('\n3. Testing sample time log data...');
    const sampleLogs = await query('SELECT id, user_id, clock_in, clock_out, total_hours, status FROM time_logs LIMIT 3');
    console.log('✅ Sample time logs:');
    console.table(sampleLogs);

    console.log('\n🎉 All time service tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testTimeService();
