/**
 * Test the complete approval workflow
 */

require('dotenv').config({ path: './backend/.env' });

async function testApprovalWorkflow() {
  try {
    console.log('🧪 Testing complete approval workflow...\n');

    const { query } = require('./backend/src/shared/database');
    const approvalService = require('./backend/src/modules/approvals/approvalService');
    
    // Get a sample time log to create an approval for (one without pending approvals)
    console.log('1. Getting sample time log without pending approvals...');
    const timeLogs = await query(`
      SELECT tl.id, tl.user_id
      FROM time_logs tl
      WHERE tl.clock_out IS NOT NULL
      AND NOT EXISTS (
        SELECT 1 FROM approvals a
        WHERE a.time_log_id = tl.id AND a.status = 'pending'
      )
      LIMIT 1
    `);

    if (timeLogs.length === 0) {
      console.log('❌ No completed time logs without pending approvals found to test with');
      return;
    }

    const timeLog = timeLogs[0];
    console.log('✅ Found time log:', timeLog);

    // Create a new approval request
    console.log('\n2. Creating approval request...');
    const requestData = {
      timeLogId: timeLog.id,
      requestedBy: timeLog.user_id,
      requestType: 'correction',
      requestData: {
        clock_in_time: '2024-01-15 09:00:00',
        clock_out_time: '2024-01-15 17:30:00',
        notes: 'Corrected time entry for accurate hours'
      },
      reason: 'Need to correct clock in/out times due to system glitch'
    };

    const newApproval = await approvalService.createApprovalRequest(requestData);
    console.log('✅ Approval request created:', {
      id: newApproval.id,
      status: newApproval.status,
      request_type: newApproval.request_type
    });

    // Get the approval by ID
    console.log('\n3. Retrieving approval by ID...');
    const retrievedApproval = await approvalService.getApprovalById(newApproval.id);
    console.log('✅ Retrieved approval:', {
      id: retrievedApproval.id,
      reason: retrievedApproval.reason,
      requester_name: `${retrievedApproval.requester_first_name} ${retrievedApproval.requester_last_name}`
    });

    // Process the approval (approve it)
    console.log('\n4. Processing approval (approving)...');
    const processedApproval = await approvalService.processApproval(
      newApproval.id,
      retrievedApproval.assigned_to, // Use the assigned manager
      'approved',
      'Approved - correction looks valid'
    );
    console.log('✅ Approval processed:', {
      id: processedApproval.id,
      status: processedApproval.status,
      approved_by: processedApproval.approved_by
    });

    // Verify the time log was updated
    console.log('\n5. Checking if time log was updated...');
    const updatedTimeLog = await query('SELECT * FROM time_logs WHERE id = ?', [timeLog.id]);
    console.log('✅ Time log after correction:', {
      id: updatedTimeLog[0].id,
      clock_in: updatedTimeLog[0].clock_in,
      clock_out: updatedTimeLog[0].clock_out,
      total_hours: updatedTimeLog[0].total_hours
    });

    console.log('\n🎉 Complete approval workflow test passed!');

  } catch (error) {
    console.error('❌ Workflow test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testApprovalWorkflow();
