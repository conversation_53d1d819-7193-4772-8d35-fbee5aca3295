/**
 * Home page component - Landing page for Flexair Timekeeping
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();

  useEffect(() => {
    // Redirect authenticated users to dashboard
    if (!loading && isAuthenticated) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, loading, router]);

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            Flexair Timekeeping
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Modern timekeeping application with biometric integration,
            comprehensive analytics, and seamless workforce management.
          </p>
        </div>

        {/* Feature Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          <FeatureCard
            title="Biometric Integration"
            description="Secure fingerprint and facial recognition for accurate time tracking"
            icon="🔐"
          />
          <FeatureCard
            title="Real-time Analytics"
            description="Comprehensive dashboard with insights and reporting capabilities"
            icon="📊"
          />
          <FeatureCard
            title="Approval Workflow"
            description="Streamlined approval process for time corrections and requests"
            icon="✅"
          />
          <FeatureCard
            title="Role-based Access"
            description="Admin, manager, and employee roles with appropriate permissions"
            icon="👥"
          />
          <FeatureCard
            title="Audit Logging"
            description="Complete audit trail for all system activities and changes"
            icon="📝"
          />
          <FeatureCard
            title="REST API"
            description="Modern Express.js backend with comprehensive API endpoints"
            icon="🚀"
          />
        </div>

        {/* Authentication Actions */}
        <div className="text-center mb-8">
          <div className="space-y-3 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
            <Link
              href="/auth/login"
              className="btn btn-primary btn-lg"
            >
              Sign In
            </Link>
            <Link
              href="/auth/register"
              className="btn btn-outline btn-lg"
            >
              Create Account
            </Link>
          </div>
        </div>

        {/* Status Section */}
        <div className="compact-card text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-3">
            Development Status
          </h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-semibold text-green-600 mb-2">
                ✅ Backend Complete
              </h3>
              <ul className="text-left text-gray-600 space-y-1">
                <li>• Authentication with JWT</li>
                <li>• User Management</li>
                <li>• Time Tracking</li>
                <li>• Approval Workflow</li>
                <li>• Biometric Integration</li>
                <li>• Dashboard Analytics</li>
              </ul>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-green-600 mb-2">
                ✅ Frontend Authentication
              </h3>
              <ul className="text-left text-gray-600 space-y-1">
                <li>• Login & Registration Forms</li>
                <li>• Protected Routes</li>
                <li>• JWT Token Management</li>
                <li>• Authentication Context</li>
                <li>• Auto Token Refresh</li>
                <li>• Role-based Access Control</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">
              API Status: <span className="text-green-600 font-semibold">Running on http://localhost:5002</span>
            </p>
            <p className="text-sm text-gray-500 mt-1">
              Frontend: <span className="text-green-600 font-semibold">Authentication System Ready</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Feature card component
 */
function FeatureCard({ title, description, icon }: {
  title: string;
  description: string;
  icon: string;
}) {
  return (
    <div className="compact-card text-center hover:shadow-lg transition-shadow">
      <div className="text-3xl mb-3">{icon}</div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  );
}