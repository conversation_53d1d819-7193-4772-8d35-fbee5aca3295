"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/time/page",{

/***/ "(app-pages-browser)/./src/components/approvals/ApprovalRequestForm.tsx":
/*!**********************************************************!*\
  !*** ./src/components/approvals/ApprovalRequestForm.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApprovalRequestForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_approvals__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/approvals */ \"(app-pages-browser)/./src/lib/approvals.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n/**\n * ApprovalRequestForm Component\n * Form for employees to submit approval requests for time corrections, overtime, or leave\n */ function ApprovalRequestForm(param) {\n    let { timeLogId, currentClockIn = \"\", currentClockOut = \"\", onSuccess, onCancel } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        requestType: \"correction\",\n        reason: \"\",\n        clockIn: currentClockIn,\n        clockOut: currentClockOut,\n        overtimeHours: \"\",\n        overtimeDate: \"\",\n        leaveStartDate: \"\",\n        leaveEndDate: \"\",\n        leaveType: \"sick\"\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    /**\n   * Handle form field changes\n   */ const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when user starts typing\n        if (error) {\n            setError(null);\n        }\n    };\n    /**\n   * Validate form data\n   */ const validateForm = ()=>{\n        if (!formData.reason.trim()) {\n            return \"Reason is required\";\n        }\n        if (formData.requestType === \"correction\") {\n            if (!formData.clockIn) {\n                return \"Clock in time is required for corrections\";\n            }\n            if (!formData.clockOut) {\n                return \"Clock out time is required for corrections\";\n            }\n        }\n        if (formData.requestType === \"overtime\") {\n            if (!formData.overtimeHours || parseFloat(formData.overtimeHours) <= 0) {\n                return \"Valid overtime hours are required\";\n            }\n            if (!formData.overtimeDate) {\n                return \"Overtime date is required\";\n            }\n        }\n        if (formData.requestType === \"leave\") {\n            if (!formData.leaveStartDate) {\n                return \"Leave start date is required\";\n            }\n            if (!formData.leaveEndDate) {\n                return \"Leave end date is required\";\n            }\n            if (new Date(formData.leaveStartDate) > new Date(formData.leaveEndDate)) {\n                return \"Leave start date must be before end date\";\n            }\n        }\n        return null;\n    };\n    /**\n   * Prepare request data based on request type\n   */ const prepareRequestData = ()=>{\n        switch(formData.requestType){\n            case \"correction\":\n                return {\n                    clockIn: formData.clockIn,\n                    clockOut: formData.clockOut\n                };\n            case \"overtime\":\n                return {\n                    hours: parseFloat(formData.overtimeHours),\n                    date: formData.overtimeDate\n                };\n            case \"leave\":\n                return {\n                    startDate: formData.leaveStartDate,\n                    endDate: formData.leaveEndDate,\n                    type: formData.leaveType\n                };\n            default:\n                return {};\n        }\n    };\n    /**\n   * Handle form submission\n   */ const handleSubmit = async (e)=>{\n        e.preventDefault();\n        const validationError = validateForm();\n        if (validationError) {\n            setError(validationError);\n            return;\n        }\n        setIsSubmitting(true);\n        setError(null);\n        try {\n            const requestData = {\n                timeLogId,\n                requestType: formData.requestType,\n                requestData: prepareRequestData(),\n                reason: formData.reason.trim()\n            };\n            await (0,_lib_approvals__WEBPACK_IMPORTED_MODULE_2__.createApprovalRequest)(requestData);\n            if (onSuccess) {\n                onSuccess();\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to submit approval request\");\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"compact-card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Request Approval\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCancel,\n                        className: \"text-gray-400 hover:text-gray-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-3 p-2 bg-red-50 border border-red-200 rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"requestType\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Request Type\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"requestType\",\n                                name: \"requestType\",\n                                value: formData.requestType,\n                                onChange: handleChange,\n                                className: \"form-input\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"correction\",\n                                        children: \"Time Correction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"overtime\",\n                                        children: \"Overtime Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"leave\",\n                                        children: \"Leave Request\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    formData.requestType === \"correction\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"clockIn\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Corrected Clock In Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"datetime-local\",\n                                        id: \"clockIn\",\n                                        name: \"clockIn\",\n                                        value: formData.clockIn,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"clockOut\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Corrected Clock Out Time\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"datetime-local\",\n                                        id: \"clockOut\",\n                                        name: \"clockOut\",\n                                        value: formData.clockOut,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this),\n                    formData.requestType === \"overtime\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"overtimeHours\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Overtime Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"number\",\n                                        id: \"overtimeHours\",\n                                        name: \"overtimeHours\",\n                                        value: formData.overtimeHours,\n                                        onChange: handleChange,\n                                        min: \"0\",\n                                        step: \"0.5\",\n                                        placeholder: \"e.g., 2.5\",\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"overtimeDate\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Overtime Date\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"date\",\n                                        id: \"overtimeDate\",\n                                        name: \"overtimeDate\",\n                                        value: formData.overtimeDate,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this),\n                    formData.requestType === \"leave\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"leaveStartDate\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"Start Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                id: \"leaveStartDate\",\n                                                name: \"leaveStartDate\",\n                                                value: formData.leaveStartDate,\n                                                onChange: handleChange,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"leaveEndDate\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                children: \"End Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                id: \"leaveEndDate\",\n                                                name: \"leaveEndDate\",\n                                                value: formData.leaveEndDate,\n                                                onChange: handleChange,\n                                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"leaveType\",\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Leave Type\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"leaveType\",\n                                        name: \"leaveType\",\n                                        value: formData.leaveType,\n                                        onChange: handleChange,\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"sick\",\n                                                children: \"Sick Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"vacation\",\n                                                children: \"Vacation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"personal\",\n                                                children: \"Personal Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"emergency\",\n                                                children: \"Emergency Leave\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"reason\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: [\n                                    \"Reason for Request \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-red-500\",\n                                        children: \"*\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"reason\",\n                                name: \"reason\",\n                                value: formData.reason,\n                                onChange: handleChange,\n                                rows: 4,\n                                placeholder: \"Please provide a detailed reason for this request...\",\n                                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 pt-4\",\n                        children: [\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isSubmitting ? \"Submitting...\" : \"Submit Request\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                                lineNumber: 364,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Workspace\\\\flexair_timekeeping_app\\\\frontend\\\\src\\\\components\\\\approvals\\\\ApprovalRequestForm.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, this);\n}\n_s(ApprovalRequestForm, \"vUUetPWoANLGCOIpO265667omSM=\");\n_c = ApprovalRequestForm;\nvar _c;\n$RefreshReg$(_c, \"ApprovalRequestForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/approvals/ApprovalRequestForm.tsx\n"));

/***/ })

});