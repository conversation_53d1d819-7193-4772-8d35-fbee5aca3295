'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { apiRequest } from '@/lib/auth';
import ApprovalRequestForm from '@/components/approvals/ApprovalRequestForm';
// import { ChevronUpIcon, ChevronDownIcon, MagnifyingGlassIcon, ArrowDownTrayIcon } from '@heroicons/react/24/outline';

interface TimeLog {
  id: number;
  user_id: number;
  clock_in_time: string;
  clock_out_time?: string;
  total_hours?: string;
  break_minutes?: number;
  overtime_minutes?: number;
  clock_in_location?: string;
  clock_out_location?: string;
  notes?: string;
  status: 'active' | 'completed' | 'approved';
  created_at: string;
  updated_at: string;
}

interface TimeLogsResponse {
  logs: TimeLog[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Time Logs component for viewing and managing time entries
 * Displays paginated list of time logs with filtering options
 */
export default function TimeLogs() {
  const { user } = useAuth();
  const [timeLogs, setTimeLogs] = useState<TimeLog[]>([]);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Filter states
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [status, setStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Approval request modal state
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedTimeLog, setSelectedTimeLog] = useState<TimeLog | null>(null);

  // Initialize with current week
  useEffect(() => {
    const now = new Date();
    const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);

    setStartDate(weekStart.toISOString().split('T')[0]);
    setEndDate(weekEnd.toISOString().split('T')[0]);
  }, []);

  // Load time logs when filters or page changes
  useEffect(() => {
    if (startDate && endDate) {
      loadTimeLogs();
    }
  }, [currentPage, startDate, endDate, status]);

  /**
   * Load time logs from API with current filters
   */
  const loadTimeLogs = async () => {
    setIsLoading(true);
    setError('');

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        start_date: startDate,
        end_date: endDate,
        ...(status !== 'all' && { status })
      });

      const response = await apiRequest(`/time/logs?${params}`);

      if (response.success && response.data) {
        const data = response.data as TimeLogsResponse;
        setTimeLogs(data.logs);
        setTotalPages(data.totalPages);
        setTotalCount(data.totalCount);
      } else {
        setError(response.message || 'Failed to load time logs');
      }
    } catch (error) {
      setError('Error loading time logs. Please try again.');
      console.error('Time logs error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle filter changes
   */
  const handleFilterChange = () => {
    setCurrentPage(1); // Reset to first page when filters change
    loadTimeLogs();
  };

  /**
   * Handle page changes
   */
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  /**
   * Format date for display
   */
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  /**
   * Format time for display
   */
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  /**
   * Get status badge color
   */
  const getStatusBadge = (status: string) => {
    const colors = {
      active: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      approved: 'bg-purple-100 text-purple-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  /**
   * Calculate total hours for the period
   */
  const getTotalHours = () => {
    return timeLogs.reduce((total, log) => {
      if (log.total_hours) {
        const hours = parseFloat(log.total_hours);
        return total + hours;
      }
      return total;
    }, 0).toFixed(2);
  };

  /**
   * Set quick date filters
   */
  const setQuickDateFilter = (days: number) => {
    const end = new Date();
    const start = new Date();
    start.setDate(end.getDate() - days);

    setStartDate(start.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  /**
   * Filter time logs based on search term
   */
  const filteredTimeLogs = timeLogs.filter(log => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    return (
      log.clock_in_location?.toLowerCase().includes(searchLower) ||
      log.notes?.toLowerCase().includes(searchLower) ||
      log.status.toLowerCase().includes(searchLower)
    );
  });

  /**
   * Open approval request modal
   */
  const openApprovalModal = (timeLog: TimeLog) => {
    setSelectedTimeLog(timeLog);
    setShowApprovalModal(true);
  };

  /**
   * Close approval request modal
   */
  const closeApprovalModal = () => {
    setShowApprovalModal(false);
    setSelectedTimeLog(null);
  };

  /**
   * Handle approval request success
   */
  const handleApprovalSuccess = () => {
    closeApprovalModal();
    // Optionally refresh the time logs to show updated status
    loadTimeLogs();
  };

  /**
   * Check if time log can be corrected
   */
  const canRequestCorrection = (timeLog: TimeLog) => {
    // Only allow correction requests for completed time logs
    return timeLog.status === 'completed' && timeLog.clock_out_time;
  };

  /**
   * Export time logs to CSV
   */
  const exportToCSV = () => {
    const headers = ['Date', 'Clock In', 'Clock Out', 'Total Hours', 'Location', 'Status', 'Notes'];
    const csvData = filteredTimeLogs.map(log => [
      formatDate(log.clock_in_time),
      formatTime(log.clock_in_time),
      log.clock_out_time ? formatTime(log.clock_out_time) : '',
      log.total_hours || '',
      log.clock_in_location || '',
      log.status,
      log.notes || ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `time-logs-${startDate}-to-${endDate}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="compact-card">
      <div className="mb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-lg font-bold text-gray-800 mb-1">Time Logs</h2>
            <p className="text-gray-600">View and manage your time entries</p>
          </div>
          <div className="mt-3 sm:mt-0">
            <button
              onClick={exportToCSV}
              disabled={filteredTimeLogs.length === 0}
              className="btn btn-success"
            >
              📥 Export CSV
            </button>
          </div>
        </div>
      </div>

      {/* Search and Quick Filters */}
      <div className="bg-gray-50 rounded-lg p-3 mb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-3">
          <h3 className="text-base font-semibold text-gray-800 mb-2 lg:mb-0">Search & Filters</h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setQuickDateFilter(0)}
              className="btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800"
            >
              Today
            </button>
            <button
              onClick={() => setQuickDateFilter(7)}
              className="btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800"
            >
              Last 7 Days
            </button>
            <button
              onClick={() => setQuickDateFilter(30)}
              className="btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800"
            >
              Last 30 Days
            </button>
            <button
              onClick={() => setQuickDateFilter(90)}
              className="btn btn-sm bg-blue-100 hover:bg-blue-200 text-blue-800"
            >
              Last 3 Months
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mb-3">
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</span>
            <input
              type="text"
              placeholder="Search by location, notes, or status..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pl-10"
            />
          </div>
        </div>

        {/* Date and Status Filters */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="form-input"
            />
          </div>
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="form-input"
            />
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="form-input"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="approved">Approved</option>
            </select>
          </div>
          <div className="flex items-end">
            <button
              onClick={handleFilterChange}
              disabled={isLoading}
              className="btn btn-primary w-full"
            >
              {isLoading ? 'Loading...' : 'Apply Filters'}
            </button>
          </div>
        </div>
      </div>

      {/* Summary */}
      {timeLogs.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-blue-600">Total Entries</p>
              <p className="text-base font-bold text-blue-800">{totalCount}</p>
            </div>
            <div>
              <p className="text-sm text-blue-600">Showing</p>
              <p className="text-base font-bold text-blue-800">{filteredTimeLogs.length}</p>
            </div>
            <div>
              <p className="text-sm text-blue-600">Total Hours</p>
              <p className="text-base font-bold text-blue-800">{getTotalHours()}</p>
            </div>
            <div>
              <p className="text-sm text-blue-600">Date Range</p>
              <p className="font-semibold text-blue-800">
                {formatDate(startDate)} - {formatDate(endDate)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-200 text-red-700 px-3 py-2 rounded-lg mb-4">
          {error}
        </div>
      )}

      {/* Time Logs Table */}
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading time logs...</span>
        </div>
      ) : filteredTimeLogs.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {timeLogs.length === 0
              ? "No time logs found for the selected period."
              : "No time logs match your search criteria."}
          </p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="table">
            <thead className="table-header">
              <tr>
                <th className="table-header-cell">
                  Date
                </th>
                <th className="table-header-cell">
                  Clock In
                </th>
                <th className="table-header-cell">
                  Clock Out
                </th>
                <th className="table-header-cell">
                  Total Hours
                </th>
                <th className="table-header-cell">
                  Location
                </th>
                <th className="table-header-cell">
                  Status
                </th>
                <th className="table-header-cell">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTimeLogs.map((log) => (
                <tr key={log.id} className="table-row">
                  <td className="table-cell">
                    {formatDate(log.clock_in_time)}
                  </td>
                  <td className="table-cell">
                    {formatTime(log.clock_in_time)}
                  </td>
                  <td className="table-cell">
                    {log.clock_out_time ? formatTime(log.clock_out_time) : '-'}
                  </td>
                  <td className="table-cell font-medium">
                    {log.total_hours || '-'}
                  </td>
                  <td className="table-cell text-gray-500">
                    {log.clock_in_location || 'Not specified'}
                  </td>
                  <td className="table-cell">
                    <span className={`badge ${getStatusBadge(log.status)}`}>
                      {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                    </span>
                  </td>
                  <td className="table-cell font-medium">
                    {canRequestCorrection(log) ? (
                      <button
                        onClick={() => openApprovalModal(log)}
                        className="text-blue-600 hover:text-blue-900 transition duration-200"
                      >
                        Request Correction
                      </button>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-700">
            Showing page {currentPage} of {totalPages} ({totalCount} total entries)
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200"
            >
              Previous
            </button>
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`px-3 py-1 text-sm rounded-md transition duration-200 ${
                    currentPage === page
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300'
                  }`}
                >
                  {page}
                </button>
              );
            })}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 disabled:bg-gray-100 disabled:text-gray-400 rounded-md transition duration-200"
            >
              Next
            </button>
          </div>
        </div>
      )}

      {/* Approval Request Modal */}
      {showApprovalModal && selectedTimeLog && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-4 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <ApprovalRequestForm
              timeLogId={selectedTimeLog.id}
              currentClockIn={selectedTimeLog.clock_in_time}
              currentClockOut={selectedTimeLog.clock_out_time || ''}
              onSuccess={handleApprovalSuccess}
              onCancel={closeApprovalModal}
            />
          </div>
        </div>
      )}
    </div>
  );
}