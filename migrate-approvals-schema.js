/**
 * Migration script to update approvals table schema
 * This script migrates the old approvals table structure to the new one
 */

const mysql = require('mysql2/promise');
require('dotenv').config({ path: './backend/.env' });

async function migrateApprovalsSchema() {
  let connection;
  
  try {
    console.log('🔗 Connecting to database...');
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'flexair_timekeeping'
    });

    console.log('📊 Checking current approvals table structure...');
    const [currentStructure] = await connection.execute('DESCRIBE approvals');
    console.table(currentStructure);

    // Check if migration is needed
    const hasOldSchema = currentStructure.some(col => col.Field === 'user_id' || col.Field === 'manager_id');
    const hasNewSchema = currentStructure.some(col => col.Field === 'requested_by' || col.Field === 'assigned_to');

    if (!hasOldSchema && hasNewSchema) {
      console.log('✅ Schema is already up to date!');
      return;
    }

    if (hasOldSchema && hasNewSchema) {
      console.log('⚠️  Mixed schema detected. Manual intervention may be required.');
      return;
    }

    console.log('🔄 Starting migration...');

    // Step 1: Backup existing data
    console.log('📦 Backing up existing approvals data...');
    const [existingData] = await connection.execute('SELECT * FROM approvals');
    console.log(`Found ${existingData.length} existing approval records`);

    // Step 2: Add new columns
    console.log('➕ Adding new columns...');
    
    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN requested_by INT NOT NULL DEFAULT 0 AFTER time_log_id');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN assigned_to INT NOT NULL DEFAULT 0 AFTER requested_by');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN request_type ENUM("correction", "overtime", "leave") NOT NULL DEFAULT "correction" AFTER assigned_to');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN request_data TEXT AFTER request_type');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN reason TEXT NOT NULL DEFAULT "" AFTER request_data');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD COLUMN approved_by INT NULL AFTER comments');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) throw e;
    }

    // Step 3: Migrate existing data
    console.log('🔄 Migrating existing data...');
    for (const record of existingData) {
      await connection.execute(`
        UPDATE approvals 
        SET 
          requested_by = ?,
          assigned_to = ?,
          request_type = 'correction',
          reason = COALESCE(comments, 'Legacy approval request'),
          approved_by = CASE WHEN status != 'pending' THEN ? ELSE NULL END
        WHERE id = ?
      `, [record.user_id, record.manager_id, record.manager_id, record.id]);
    }

    // Step 4: Add foreign key constraints
    console.log('🔗 Adding foreign key constraints...');
    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_requested_by FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_assigned_to FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE CASCADE');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('ALTER TABLE approvals ADD CONSTRAINT fk_approvals_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL');
    } catch (e) {
      if (!e.message.includes('Duplicate foreign key constraint name')) console.warn('Warning:', e.message);
    }

    // Step 5: Add indexes
    console.log('📇 Adding indexes...');
    try {
      await connection.execute('CREATE INDEX idx_requested_by ON approvals (requested_by)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_assigned_to ON approvals (assigned_to)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_approved_by ON approvals (approved_by)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    try {
      await connection.execute('CREATE INDEX idx_request_type ON approvals (request_type)');
    } catch (e) {
      if (!e.message.includes('Duplicate key name')) console.warn('Warning:', e.message);
    }

    // Step 6: Remove old columns (optional - commented out for safety)
    console.log('🗑️  Old columns (user_id, manager_id) are kept for safety. Remove manually if needed.');
    /*
    console.log('🗑️  Removing old columns...');
    await connection.execute('ALTER TABLE approvals DROP FOREIGN KEY approvals_ibfk_2');
    await connection.execute('ALTER TABLE approvals DROP FOREIGN KEY approvals_ibfk_3');
    await connection.execute('ALTER TABLE approvals DROP INDEX idx_user_id');
    await connection.execute('ALTER TABLE approvals DROP INDEX idx_manager_id');
    await connection.execute('ALTER TABLE approvals DROP COLUMN user_id');
    await connection.execute('ALTER TABLE approvals DROP COLUMN manager_id');
    */

    console.log('📊 Checking updated table structure...');
    const [newStructure] = await connection.execute('DESCRIBE approvals');
    console.table(newStructure);

    console.log('✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run migration
if (require.main === module) {
  migrateApprovalsSchema();
}

module.exports = { migrateApprovalsSchema };
