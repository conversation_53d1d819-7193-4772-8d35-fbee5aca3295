/**
 * Simple test to check if API is working
 */

const axios = require('axios');

async function simpleTest() {
  try {
    console.log('Testing health endpoint...');
    const response = await axios.get('http://localhost:5002/health');
    console.log('✅ Health check response:', response.data);
    
    console.log('\nTesting login...');
    const loginResponse = await axios.post('http://localhost:5002/api/auth/login', {
      email: '<EMAIL>',
      password: 'manager123'
    });
    console.log('✅ Login response:', loginResponse.data.success ? 'Success' : 'Failed');
    
    if (loginResponse.data.success) {
      const token = loginResponse.data.accessToken;
      
      console.log('\nTesting approvals endpoint...');
      const approvalsResponse = await axios.get('http://localhost:5002/api/approvals', {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Approvals response:', {
        success: approvalsResponse.data.success,
        count: approvalsResponse.data.approvals?.length || 0
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

simpleTest();
